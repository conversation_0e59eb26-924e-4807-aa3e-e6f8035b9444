repo_url,slug,readme,stars,stored_at,seo_title,seo_meta_description,seo_keywords,seo_structured_data,language,category,subcategory,installation_command,tools_list,config_example,dependencies,license,last_updated,author,description_short
https://github.com/mberg/kokoro-tts-mcp,mberg/kokoro-tts-mcp,"## Kokoro Text to Speech (TTS) MCP Server

Kokoro Text to Speech MCP server that generates .mp3 files with option to upload to S3.

Uses: https://huggingface.co/spaces/hexgrad/Kokoro-TTS

## Configuration

* Clone to a local repo.
* Download the [Kokoro Onnx Weights](https://github.com/thewh1teagle/kokoro-onnx) for [kokoro-v1.0.onnx](https://github.com/thewh1teagle/kokoro-onnx/releases/download/model-files-v1.0/kokoro-v1.0.onnx) and [voices-v1.0.bin](https://github.com/thewh1teagle/kokoro-onnx/releases/download/model-files-v1.0/voices-v1.0.bin) and store in the same repo.

Add the following to your MCP configs. Update with your own values.

```
  ""kokoro-tts-mcp"": {
      ""command"": ""uv"",
      ""args"": [
        ""--directory"",
        ""/path/toyourlocal/kokoro-tts-mcp"",
        ""run"",
        ""mcp-tts.py""
      ],
      ""env"": {
        ""TTS_VOICE"": ""af_heart"",
        ""TTS_SPEED"": ""1.0"",
        ""TTS_LANGUAGE"": ""en-us"",
        ""AWS_ACCESS_KEY_ID"": """",
        ""AWS_SECRET_ACCESS_KEY"": """",
        ""AWS_REGION"": ""us-east-1"",
        ""AWS_S3_FOLDER"": ""mp3"",
        ""S3_ENABLED"": ""true"",
        ""MP3_FOLDER"": ""/path/to/mp3""
      } 
    }
```

### Install ffmmeg

This is needed to convert .wav to .mp3 files

For mac:

``` 
brew install ffmpeg
```

To run locally add these to your .env file.  See env.example and copy to .env and modify with your own values.

### Supported Environment Variables

- `AWS_ACCESS_KEY_ID`: Your AWS access key ID
- `AWS_SECRET_ACCESS_KEY`: Your AWS secret access key
- `AWS_S3_BUCKET_NAME`: S3 bucket name
- `AWS_S3_REGION`: S3 region (e.g., us-east-1)
- `AWS_S3_FOLDER`: Folder path within the S3 bucket
- `AWS_S3_ENDPOINT_URL`: Optional custom endpoint URL for S3-compatible storage
- `MCP_HOST`: Host to bind the server to (default: 0.0.0.0)
- `MCP_PORT`: Port to listen on (default: 9876)
- `MCP_CLIENT_HOST`: Hostname for client connections to the server (default: localhost)
- `DEBUG`: Enable debug mode (set to ""true"" or ""1"")
- `S3_ENABLED`: Enable S3 uploads (set to ""true"" or ""1"")
- `MP3_FOLDER`: Path to store MP3 files (default is 'mp3' folder in script directory)
- `MP3_RETENTION_DAYS`: Number of days to keep MP3 files before automatic deletion
- `DELETE_LOCAL_AFTER_S3_UPLOAD`: Whether to delete local MP3 files after successful S3 upload (set to ""true"" or ""1"")
- `TTS_VOICE`: Default voice for the TTS client (default: af_heart)
- `TTS_SPEED`: Default speed for the TTS client (default: 1.0)
- `TTS_LANGUAGE`: Default language for the TTS client (default: en-us)

## Running the Server Locally

Preferred method use UV 
```
uv run mcp-tts.py
```


## Using the TTS Client

The `mcp_client.py` script allows you to send TTS requests to the server. It can be used as follows:

### Connection Settings

When running the server and client on the same machine:
- Server should bind to `0.0.0.0` (all interfaces) or `127.0.0.1` (localhost only)
- Client should connect to `localhost` or `127.0.0.1`


### Basic Usage

```bash
python mcp_client.py --text ""Hello, world!""
```

### Reading Text from a File

```bash
python mcp_client.py --file my_text.txt
```

### Customizing Voice and Speed

```bash
python mcp_client.py --text ""Hello, world!"" --voice ""en_female"" --speed 1.2
```

### Disabling S3 Upload

```bash
python mcp_client.py --text ""Hello, world!"" --no-s3
```

### Command-line Options

```bash
python mcp_client.py --help
```

## MP3 File Management

The TTS server generates MP3 files that are stored locally and optionally uploaded to S3. You can configure how these files are managed:

### Local Storage

- Set `MP3_FOLDER` in your `.env` file to specify where MP3 files are stored
- Files are kept in this folder unless automatically deleted

### Automatic Cleanup

- Set `MP3_RETENTION_DAYS=30` (or any number) to automatically delete files older than that number of days
- Set `DELETE_LOCAL_AFTER_S3_UPLOAD=true` to delete local files immediately after successful S3 upload

### S3 Integration

- Enable/disable S3 uploads with `S3_ENABLED=true` or `DISABLE_S3=true`
- Configure AWS credentials and bucket settings in the `.env` file
- S3 uploads can be disabled per-request using the client's `--no-s3` option

","Star
 41",2025-06-24 07:59:40.920526,kokoro-tts-mcp - MCP Server | Model Context Protocol Integration,"## Kokoro Text to Speech (TTS) MCP Server

Kokoro Text to Speech MCP server that generates .mp3 files with option to upload to S3.

Uses: https://huggingface...","['mcp server', 'model context protocol', 'ai integration', 'kokoro-tts-mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'kokoro-tts-mcp', 'description': '## Kokoro Text to Speech (TTS) MCP Server\n\nKokoro Text to Speech MCP server that generates .mp3 files with option to upload to S3.\n\nUses: https://huggingface...', 'url': 'https://github.com/mberg/kokoro-tts-mcp', 'codeRepository': 'https://github.com/mberg/kokoro-tts-mcp', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Python,File Management,Cloud Services,,,,MP3_RETENTION_DAYS,,,mberg,Kokoro Text to Speech MCP server that generates .mp3 files with option to upload to S3.
https://github.com/last9/last9-mcp-server,last9/last9-mcp-server,"# Last9 MCP Server

![last9 mcp demo](mcp-demo.gif)

A [Model Context Protocol](https://modelcontextprotocol.io/) server
implementation for [Last9](https://last9.io/mcp/) that enables AI agents to
seamlessly bring real-time production context — logs, metrics, and traces — into
your local environment to auto-fix code faster.

- [View demo](https://www.youtube.com/watch?v=AQH5xq6qzjI)
- Read our
  [announcement blog post](https://last9.io/blog/launching-last9-mcp-server/)

## Status

Works with Claude desktop app, or Cursor, Windsurf, and VSCode (Github Copilot)
IDEs. Implements the following MCP
[tools](https://modelcontextprotocol.io/docs/concepts/tools):

- `get_exceptions`: Get the list of exceptions.
- `get_service_graph`: Get service graph for an endpoint from the exception.
- `get_logs`: Get logs filtered by service name and/or severity level.
- `get_drop_rules`: Get drop rules for logs that determine what logs get
  filtered out at [Last9 Control Plane](https://last9.io/control-plane)
- `add_drop_rule`: Create a drop rule for logs at
  [Last9 Control Plane](https://last9.io/control-plane)

## Tools Documentation

### get_exceptions

Retrieves server-side exceptions over a specified time range.

Parameters:

- `limit` (integer, optional): Maximum number of exceptions to return.
  Default: 20.
- `lookback_minutes` (integer, recommended): Number of minutes to look back from
  now. Default: 60. Examples: 60, 30, 15.
- `start_time_iso` (string, optional): Start time in ISO format (YYYY-MM-DD
  HH:MM:SS). Leave empty to use lookback_minutes.
- `end_time_iso` (string, optional): End time in ISO format (YYYY-MM-DD
  HH:MM:SS). Leave empty to default to current time.
- `span_name` (string, optional): Name of the span to filter by.

### get_service_graph

Gets the upstream and downstream services for a given span name, along with the
throughput for each service.

Parameters:

- `span_name` (string, required): Name of the span to get dependencies for.
- `lookback_minutes` (integer, recommended): Number of minutes to look back from
  now. Default: 60. Examples: 60, 30, 15.
- `start_time_iso` (string, optional): Start time in ISO format (YYYY-MM-DD
  HH:MM:SS). Leave empty to use lookback_minutes.

### get_logs

Gets logs filtered by optional service name and/or severity level within a
specified time range.

Parameters:

- `service` (string, optional): Name of the service to get logs for.
- `severity` (string, optional): Severity of the logs to get.
- `lookback_minutes` (integer, recommended): Number of minutes to look back from
  now. Default: 60. Examples: 60, 30, 15.
- `start_time_iso` (string, optional): Start time in ISO format (YYYY-MM-DD
  HH:MM:SS). Leave empty to use lookback_minutes.
- `end_time_iso` (string, optional): End time in ISO format (YYYY-MM-DD
  HH:MM:SS). Leave empty to default to current time.
- `limit` (integer, optional): Maximum number of logs to return. Default: 20.

### get_drop_rules

Gets drop rules for logs, which determine what logs get filtered out from
reaching Last9.

### add_drop_rule

Adds a new drop rule to filter out specific logs at
[Last9 Control Plane](https://last9.io/control-plane)

Parameters:

- `name` (string, required): Name of the drop rule.
- `filters` (array, required): List of filter conditions to apply. Each filter
  has:
  - `key` (string, required): The key to filter on. Only attributes and
    resource.attributes keys are supported. For resource attributes, use format:
    resource.attributes[key_name] and for log attributes, use format:
    attributes[key_name] Double quotes in key names must be escaped.
  - `value` (string, required): The value to filter against.
  - `operator` (string, required): The operator used for filtering. Valid
    values:
    - ""equals""
    - ""not_equals""
  - `conjunction` (string, required): The logical conjunction between filters.
    Valid values:
    - ""and""

## Installation

You can install the Last9 Observability MCP server using either:

### Homebrew

```
# Add the Last9 tap
brew tap last9/tap

# Install the Last9 MCP CLI
brew install last9-mcp
```

### NPM

```bash
# Install globally
npm install -g @last9/mcp-server

# Or run directly with npx
npx @last9/mcp-server
```

## Configuration

### Environment Variables

The Last9 MCP server requires the following environment variables:

- `LAST9_BASE_URL`: (required) Last9 API URL from
  [OTel integration](https://app.last9.io/integrations?integration=OpenTelemetry)
- `LAST9_AUTH_TOKEN`: (required) Authentication token for Last9 MCP server from
  [OTel integration](https://app.last9.io/integrations?integration=OpenTelemetry)
- `LAST9_REFRESH_TOKEN`: (required) Refresh Token with Write permissions, needed
  for accessing control plane APIs from
  [API Access](https://app.last9.io/settings/api-access)

## Usage with Claude Desktop

Configure the Claude app to use the MCP server:

1. Open the Claude Desktop app, go to Settings, then Developer
2. Click Edit Config
3. Open the `claude_desktop_config.json` file
4. Copy and paste the server config to your existing file, then save
5. Restart Claude

```json
{
  ""mcpServers"": {
    ""last9"": {
      ""command"": ""/opt/homebrew/bin/last9-mcp"",
      ""env"": {
        ""LAST9_BASE_URL"": ""<last9_otlp_host>"",
        ""LAST9_AUTH_TOKEN"": ""<last9_otlp_auth_token>"",
        ""LAST9_REFRESH_TOKEN"": ""<last9_write_refresh_token>""
      }
    }
  }
}
```

## Usage with Cursor

Configure Cursor to use the MCP server:

1. Open Cursor, go to Settings, then Cursor Settings
2. Select MCP on the left
3. Click Add ""New Global MCP Server"" at the top right
4. Copy and paste the server config to your existing file, then save
5. Restart Cursor

```json
{
  ""mcpServers"": {
    ""last9"": {
      ""command"": ""/opt/homebrew/bin/last9-mcp"",
      ""env"": {
        ""LAST9_BASE_URL"": ""<last9_otlp_host>"",
        ""LAST9_AUTH_TOKEN"": ""<last9_otlp_auth_token>"",
        ""LAST9_REFRESH_TOKEN"": ""<last9_write_refresh_token>""
      }
    }
  }
}
```

## Usage with Windsurf

Configure Windsurf to use the MCP server:

1. Open Windsurf, go to Settings, then Developer
2. Click Edit Config
3. Open the `windsurf_config.json` file
4. Copy and paste the server config to your existing file, then save
5. Restart Windsurf

```json
{
  ""mcpServers"": {
    ""last9"": {
      ""command"": ""/opt/homebrew/bin/last9-mcp"",
      ""env"": {
        ""LAST9_BASE_URL"": ""<last9_otlp_host>"",
        ""LAST9_AUTH_TOKEN"": ""<last9_otlp_auth_token>"",
        ""LAST9_REFRESH_TOKEN"": ""<last9_write_refresh_token>""
      }
    }
  }
}
```

## Usage with VS Code

> Note: MCP support in VS Code is available starting v1.99 and is currently in
> preview. For advanced configuration options and alternative setup methods,
> [view the VS Code MCP documentation](https://code.visualstudio.com/docs/copilot/chat/mcp-servers).

1.  Open VS Code, go to Settings, select the User tab, then Features, then Chat
2.  Click ""Edit settings.json""
3.  Copy and paste the server config to your existing file, then save
4.  Restart VS Code

```json
{
  ""mcp"": {
    ""servers"": {
      ""last9"": {
        ""type"": ""stdio"",
        ""command"": ""/opt/homebrew/bin/last9-mcp"",
        ""env"": {
          ""LAST9_BASE_URL"": ""<last9_otlp_host>"",
          ""LAST9_AUTH_TOKEN"": ""<last9_otlp_auth_token>"",
          ""LAST9_REFRESH_TOKEN"": ""<last9_write_refresh_token>""
        }
      }
    }
  }
}
```

## Badges

[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/last9-last9-mcp-server-badge.png)](https://mseep.ai/app/last9-last9-mcp-server)
","Star
 33",2025-06-24 07:59:40.920526,last9-mcp-server - MCP Server | Model Context Protocol Integration,"# Last9 MCP Server

![last9 mcp demo](mcp-demo.gif)

A [Model Context Protocol](https://modelcontextprotocol.io/) server
implementation for [Last9](https://l...","['mcp server', 'model context protocol', 'ai integration', 'last9-mcp-server']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'last9-mcp-server', 'description': '# Last9 MCP Server\n\n![last9 mcp demo](mcp-demo.gif)\n\nA [Model Context Protocol](https://modelcontextprotocol.io/) server\nimplementation for [Last9](https://l...', 'url': 'https://github.com/last9/last9-mcp-server', 'codeRepository': 'https://github.com/last9/last9-mcp-server', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Rust,API Integration,Time Management,npm install -g @last9/mcp-server,"Homebrew, NPM, add_drop_rule, get_drop_rules, get_exceptions, get_logs, get_service_graph","{
  ""mcpServers"": {
    ""last9"": {
      ""command"": ""/opt/homebrew/bin/last9-mcp"",
      ""env"": {
        ""LAST9_BASE_URL"": ""<last9_otlp_host>"",
        ""LAST9_AUTH_TOKEN"": ""<last9_otlp_auth_token>"",
        ""LAST9_REFRESH_TOKEN"": ""<last9_write_refresh_token>""
      }
    }
  }
}","-g, @last9/mcp-server",,,last9,A Model Context Protocol server
https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server,hannesrudolph/imessage-query-fastmcp-mcp-server,"[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/hannesrudolph-imessage-query-fastmcp-mcp-server-badge.png)](https://mseep.ai/app/hannesrudolph-imessage-query-fastmcp-mcp-server)

# iMessage Query MCP Server

An MCP server that provides safe access to your iMessage database through Model Context Protocol (MCP). This server is built with the FastMCP framework and the imessagedb library, enabling LLMs to query and analyze iMessage conversations with proper phone number validation and automatic macOS permission handling.

## 📋 System Requirements

- macOS (required for iMessage database access)
- Python 3.12+ (required for modern type hints)
- **uv** (modern Python package manager)
- **Full Disk Access permission** for your MCP client (Claude Desktop, Cursor, VS Code, etc.)

## 📦 Dependencies

### Install uv (Required)

This project uses `uv` for fast, reliable Python package management. Install it first:

```bash
# Install uv using Homebrew (recommended)
brew install uv

# Or install using the official installer
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### Python Dependencies

The script automatically manages its dependencies using the embedded metadata. No separate installation needed! Dependencies include:

- **fastmcp**: Framework for building Model Context Protocol servers
- **imessagedb**: Python library for accessing and querying the macOS Messages database
- **phonenumbers**: Google's phone number handling library for proper number validation and formatting

All dependencies are automatically installed when the script runs via `uv`.

## 📑 Table of Contents
- [System Requirements](#-system-requirements)
- [Dependencies](#-dependencies)
- [MCP Tools](#%EF%B8%8F-mcp-tools)
- [Getting Started](#-getting-started)
- [Installation Options](#-installation-options)
  - [Claude Desktop](#option-1-install-for-claude-desktop)
  - [Cline VSCode Plugin](#option-2-install-for-cline-vscode-plugin)
- [macOS Permissions Setup](#-macos-permissions-setup)
- [Safety Features](#-safety-features)
- [Development Documentation](#-development-documentation)
- [Environment Variables](#%EF%B8%8F-environment-variables)

## 🛠️ MCP Tools

The server exposes the following tools to LLMs:

### get_chat_transcript
Retrieve message history for a specific phone number with optional date filtering.

**Parameters:**
- `phone_number` (required): Phone number in any format (E.164 format preferred)
- `start_date` (optional): Start date in ISO format (YYYY-MM-DD)
- `end_date` (optional): End date in ISO format (YYYY-MM-DD)

**Features:**
- Automatic phone number validation and formatting
- Message text and timestamps
- Attachment information with missing file detection
- Date range filtering (defaults to last 7 days if no dates specified)
- Sender identification (is_from_me flag)

## 🚀 Getting Started

Clone the repository:

```bash
git clone https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server.git
cd imessage-query-fastmcp-mcp-server
```

## 📦 Installation Options

You can install this MCP server in Claude Desktop, Cline VSCode plugin, or any other MCP client. Choose the option that best suits your needs.

### Option 1: Claude Desktop

1. **Find your Claude Desktop config file:**
   - **Location**: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - Create the file if it doesn't exist

2. **Add the server configuration:**

```json
{
  ""mcpServers"": {
    ""imessage-query"": {
      ""command"": ""/full/path/to/imessage-query-server.py""
    }
  }
}
```

3. **Replace the path** with the full path to your cloned repository (e.g., `/Users/<USER>/Projects/imessage-query-fastmcp-mcp-server/imessage-query-server.py`)

4. **Restart Claude Desktop** completely (Cmd+Q, then relaunch)

### Option 2: Cline VSCode Plugin

To use this server with the [Cline VSCode plugin](http://cline.bot):

1. In VSCode, click the server icon (☰) in the Cline plugin sidebar
2. Click the ""Edit MCP Settings"" button (✎)
3. Add the following configuration to the settings file:

```json
{
  ""imessage-query"": {
    ""command"": ""/full/path/to/imessage-query-server.py""
  }
}
```

4. **Replace the path** with the full path to your cloned repository

### Option 3: Other MCP Clients

For other MCP clients, use the direct script path as the command:

```
/full/path/to/imessage-query-server.py
```

The script's shebang (`#!/usr/bin/env -S uv run --script`) handles dependency management automatically.

> **Note**: This simplified configuration replaces the previous FastMCP installation method. The script is now self-contained and manages its own dependencies through `uv`.

## 🔐 macOS Permissions Setup

This server requires **Full Disk Access** permission to read the iMessage database. The server includes intelligent permission detection and will guide you through the setup process.

### Automatic Permission Detection

When you first use the server, it will:
1. **Detect your MCP client** (Claude Desktop, Cursor, VS Code, etc.)
2. **Check for Full Disk Access** permission
3. **Automatically open System Preferences** to the correct settings panel
4. **Provide step-by-step instructions** specific to your application

### Manual Permission Setup

If automatic detection doesn't work, follow these steps:

1. **Open System Preferences** → **Privacy & Security** → **Full Disk Access**
2. **Click the lock icon** and enter your password to make changes
3. **Click the '+' button** to add an application
4. **Navigate to and select your MCP client:**
   - **Claude Desktop**: `/Applications/Claude.app`
   - **Cursor**: `/Applications/Cursor.app`
   - **VS Code**: `/Applications/Visual Studio Code.app`
5. **Restart your MCP client** completely (Cmd+Q, then relaunch)

### Common Issues

- **Permission denied errors**: Make sure you've restarted your MCP client after granting permission
- **""uv"" instead of app name**: The server will auto-detect your actual MCP client and provide correct instructions
- **Database not found**: Ensure you've used the Messages app and iMessage is enabled

### Security Note

This server only requires **read access** to your iMessage database. It cannot modify, delete, or send messages.

## 🔒 Safety Features

- **Read-only access** to the iMessage database (cannot modify, delete, or send messages)
- **Phone number validation** using Google's phonenumbers library with proper E.164 formatting
- **Safe attachment handling** with missing file detection and metadata extraction
- **Date range validation** to prevent invalid queries
- **Progress output suppression** for clean JSON responses in MCP protocol
- **Intelligent permission detection** with automatic System Preferences navigation
- **MCP client identification** for accurate permission guidance

## 📚 Development Documentation

The repository includes comprehensive documentation for development:

- `dev_docs/imessagedb-documentation.txt`: Complete documentation about the iMessage database structure and the imessagedb library's capabilities
- `dev_docs/fastmcp-documentation.txt`: FastMCP framework details and MCP tool development
- `dev_docs/mcp-documentation.txt`: Model Context Protocol specification

This documentation serves as context when developing features and can be used with LLMs to assist in development.

## ⚙️ Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SQLITE_DB_PATH` | Custom path to iMessage database | `~/Library/Messages/chat.db` |

The server automatically locates the iMessage database in the default macOS location. The environment variable is only needed for custom database locations.

## 🔧 Advanced Usage

### Custom Database Path

If you need to use a custom database path:

```bash
export SQLITE_DB_PATH=""/path/to/custom/chat.db""
```

### Testing the Server

Test the server directly using mcptools (github.com/f/mcptools):

```bash
# Navigate to the repository directory
cd /path/to/imessage-query-fastmcp-mcp-server

# List available tools
mcp tools ./imessage-query-server.py

# Test a tool call
mcp call get_chat_transcript ./imessage-query-server.py -p '{""phone_number"": ""+1234567890""}'
```

The script will automatically handle dependency installation via `uv` when first run.

## 🐛 Troubleshooting

### Common Error Messages

**""❌ Full Disk Access permission required""**
- Follow the [macOS Permissions Setup](#-macos-permissions-setup) section
- Ensure you've restarted your MCP client after granting permission

**""Messages database not found""**
- Make sure you've used the Messages app at least once
- Verify iMessage is enabled in Messages preferences

**""Invalid phone number""**
- Phone numbers are validated using Google's phonenumbers library
- Try using E.164 format (e.g., ""+1234567890"")
- US numbers without country code will be assumed to be US numbers

### Getting Help

If you encounter issues:
1. Check the error message for specific guidance
2. Ensure your MCP client has Full Disk Access permission
3. Verify the Messages app has been used and iMessage is enabled
4. Try testing the server directly with mcptools (see Advanced Usage)
","Star
 57",2025-06-24 07:59:40.920526,imessage-query-fastmcp-mcp-server - MCP Server | Model Context Protocol Integration,[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/hannesrudolph-imessage-query-fastmcp-mcp-server-badge.png)](https://mseep.ai/app/hannesrudolph-im...,"['mcp server', 'model context protocol', 'ai integration', 'imessage-query-fastmcp-mcp-server']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'imessage-query-fastmcp-mcp-server', 'description': '[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/hannesrudolph-imessage-query-fastmcp-mcp-server-badge.png)](https://mseep.ai/app/hannesrudolph-im...', 'url': 'https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server', 'codeRepository': 'https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Python,Database,Search Engine,,get_chat_transcript,"{
  ""mcpServers"": {
    ""imessage-query"": {
      ""command"": ""/full/path/to/imessage-query-server.py""
    }
  }
}",,,,hannesrudolph,"An MCP server that provides safe access to your iMessage database through Model Context Protocol (MCP). This server is built with the FastMCP framework and the imessagedb library, enabling LLMs to que..."
https://github.com/mobile-next/mobile-mcp,mobile-next/mobile-mcp,"# Mobile Next - MCP server for Mobile Development and Automation  | iOS, Android, Simulator, Emulator, and physical devices

This is a [Model Context Protocol (MCP) server](https://github.com/modelcontextprotocol) that enables scalable mobile automation, development through a platform-agnostic interface, eliminating the need for distinct iOS or Android knowledge. You can run it on emulators, simulators, and physical devices (iOS and Android).
This server allows Agents and LLMs to interact with native iOS/Android applications and devices through structured accessibility snapshots or coordinate-based taps based on screenshots.

<h4 align=""center"">
<a href=""https://github.com/mobile-next/mobile-mcp"">
    <img src=""https://img.shields.io/github/stars/mobile-next/mobile-mcp"" alt=""Mobile Next Stars"" />
  </a>
 <a href=""https://github.com/mobile-next/mobile-mcp"">
    <img src=""https://img.shields.io/github/contributors/mobile-next/mobile-mcp?color=green"" alt=""Mobile Next Downloads"" />
  </a>
  <a href=""https://www.npmjs.com/package/@mobilenext/mobile-mcp"">
    <img src=""https://img.shields.io/npm/dm/@mobilenext/mobile-mcp?logo=npm&style=flat&color=red"" alt=""npm"">
  </a>
<a href=""https://github.com/mobile-next/mobile-mcp/releases"">
    <img src=""https://img.shields.io/github/release/mobile-next/mobile-mcp"">
  </a>
<a href=""https://github.com/mobile-next/mobile-mcp/blob/main/LICENSE"">
    <img src=""https://img.shields.io/badge/license-Apache 2.0-blue.svg"" alt=""Mobile MCP is released under the Apache-2.0 License"">
  </a>

</p>

<h4 align=""center"">
<a href=""http://mobilenexthq.com/join-slack"">
    <img src=""https://img.shields.io/badge/join-Slack-blueviolet?logo=slack&style=flat"" alt=""Slack community channel"" />
</a>
</p>

https://github.com/user-attachments/assets/c4e89c4f-cc71-4424-8184-bdbc8c638fa1

<p align=""center"">
    <a href=""https://github.com/mobile-next/"">
        <img alt=""mobile-mcp"" src=""https://raw.githubusercontent.com/mobile-next/mobile-next-assets/refs/heads/main/mobile-mcp-banner.png"" width=""600"">
    </a>
</p>

### 🚀 Mobile MCP Roadmap: Building the Future of Mobile

Join us on our journey as we continuously enhance Mobile MCP!
Check out our detailed roadmap to see upcoming features, improvements, and milestones. Your feedback is invaluable in shaping the future of mobile automation.

👉 [Explore the Roadmap](https://github.com/orgs/mobile-next/projects/3)


### Main use cases

How we help to scale mobile automation:

- 📲 Native app automation (iOS and Android) for testing or data-entry scenarios.
- 📝 Scripted flows and form interactions without manually controlling simulators/emulators or physical devices (iPhone, Samsung, Google Pixel etc)
- 🧭 Automating multi-step user journeys driven by an LLM
- 👆 General-purpose mobile application interaction for agent-based frameworks
- 🤖 Enables agent-to-agent communication for mobile automation usecases, data extraction

## Main Features

- 🚀 **Fast and lightweight**: Uses native accessibility trees for most interactions, or screenshot based coordinates where a11y labels are not available.
- 🤖 **LLM-friendly**: No computer vision model required in Accessibility (Snapshot).
- 🧿 **Visual Sense**: Evaluates and analyses what’s actually rendered on screen to decide the next action. If accessibility data or view-hierarchy coordinates are unavailable, it falls back to screenshot-based analysis.
- 📊 **Deterministic tool application**: Reduces ambiguity found in purely screenshot-based approaches by relying on structured data whenever possible.
- 📺 **Extract structured data**: Enables you to extract structred data from anything visible on screen.

## 🏗️ Mobile MCP Architecture

<p align=""center"">
    <a href=""https://raw.githubusercontent.com/mobile-next/mobile-next-assets/refs/heads/main/mobile-mcp-arch-1.png"">
        <img alt=""mobile-mcp"" src=""https://raw.githubusercontent.com/mobile-next/mobile-next-assets/refs/heads/main/mobile-mcp-arch-1.png"" width=""600"">
    </a>
</p>


## 📚 Wiki page

More details in our [wiki page](https://github.com/mobile-next/mobile-mcp/wiki) for setup, configuration and debugging related questions.


## Installation and configuration

Setup our MCP with Cline, Cursor, Claude, VS Code, Github Copilot:

```json
{
  ""mcpServers"": {
    ""mobile-mcp"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""@mobilenext/mobile-mcp@latest""]
    }
  }
}

```
[Cline:](https://docs.cline.bot/mcp/configuring-mcp-servers) To setup Cline, just add the json above to your MCP settings file.
[More in our wiki](https://github.com/mobile-next/mobile-mcp/wiki/Cline)

[Claude Code:](https://docs.anthropic.com/en/docs/agents-and-tools/claude-code/overview)

```
claude mcp add mobile -- npx -y @mobilenext/mobile-mcp@latest
```

[Read more in our wiki](https://github.com/mobile-next/mobile-mcp/wiki)! 🚀


### 🛠️ How to Use 📝

After adding the MCP server to your IDE/Client, you can instruct your AI assistant to use the available tools.
For example, in Cursor's agent mode, you could use the prompts below to quickly validate, test and iterate on UI intereactions, read information from screen, go through complex workflows.
Be descriptive, straight to the point.

### ✨ Example Prompts

#### Workflows

You can specifiy detailed workflows in a single prompt, verify business logic, setup automations. You can go crazy:

**Search for a video, comment, like and share it.**
```
Find the video called "" Beginner Recipe for Tonkotsu Ramen"" by Way of
Ramen, click on like video, after liking write a comment "" this was
delicious, will make it next Friday"", share the video with the first
contact in your whatsapp list.
```

**Download a successful step counter app, register, setup workout and 5-star the app**
```
Find and Download a free ""Pomodoro"" app that has more than 1k stars.
Launch the app, register with my email, after registration find how to
start a pomodoro timer. When the pomodoro timer started, go back to the
app store and rate the app 5 stars, and leave a comment how useful the
app is.
```

**Search in Substack, read, highlight, comment and save an article**
```
Open Substack website, search for ""Latest trends in AI automation 2025"",
open the first article, highlight the section titled ""Emerging AI trends"",
and save article to reading list for later review, comment a random
paragraph summary.
```

**Reserve a workout class, set timer**
```
Open ClassPass, search for yoga classes tomorrow morning within 2 miles,
book the highest-rated class at 7 AM, confirm reservation,
setup a timer for the booked slot in the phone
```

**Find a local event, setup calendar event**
```
Open Eventbrite, search for AI startup meetup events happening this
weekend in ""Austin, TX"", select the most popular one, register and RSVP
yes to the event, setup a calendar event as a reminder.
```

**Check weather forecast and send a Whatsapp/Telegram/Slack message**
```
Open Weather app, check tomorrow's weather forecast for ""Berlin"", and
send the summary via Whatsapp/Telegram/Slack to contact ""Lauren Trown"",
thumbs up their response.
```

- **Schedule a meeting in Zoom and share invite via email**
```
Open Zoom app, schedule a meeting titled ""AI Hackathon"" for tomorrow at
10AM with a duration of 1 hour, copy the invitation link, and send it via
Gmail to contacts ""<EMAIL>"".
```
[More prompt examples can be found here.](https://github.com/mobile-next/mobile-mcp/wiki/Prompt-Example-repo-list)

## Prerequisites

What you will need to connect MCP with your agent and mobile devices:

- [Xcode command line tools](https://developer.apple.com/xcode/resources/)
- [Android Platform Tools](https://developer.android.com/tools/releases/platform-tools)
- [node.js](https://nodejs.org/en/download/) v22+
- [MCP](https://modelcontextprotocol.io/introduction) supported foundational models or agents, like [Claude MCP](https://modelcontextprotocol.io/quickstart/server), [OpenAI Agent SDK](https://openai.github.io/openai-agents-python/mcp/), [Copilot Studio](https://www.microsoft.com/en-us/microsoft-copilot/blog/copilot-studio/introducing-model-context-protocol-mcp-in-copilot-studio-simplified-integration-with-ai-apps-and-agents/)

### Simulators, Emulators, and Physical Devices

When launched, Mobile MCP can connect to:
- iOS Simulators on macOS/Linux
- Android Emulators on Linux/Windows/macOS
- Physical iOS or Android devices (requires proper platform tools and drivers)

Make sure you have your mobile platform SDKs (Xcode, Android SDK) installed and configured properly before running Mobile Next Mobile MCP.

### Running in ""headless"" mode on Simulators/Emulators

When you do not have a physical phone connected to your machine, you can run Mobile MCP with an emulator or simulator in the background.

For example, on Android:
1. Start an emulator (avdmanager / emulator command).
2. Run Mobile MCP with the desired flags

On iOS, you'll need Xcode and to run the Simulator before using Mobile MCP with that simulator instance.
- `xcrun simctl list`
- `xcrun simctl boot ""iPhone 16""`

# Thanks to all contributors ❤️

### We appreciate everyone who has helped improve this project.

  <a href = ""https://github.com/mobile-next/mobile-mcp/graphs/contributors"">
   <img src = ""https://contrib.rocks/image?repo=mobile-next/mobile-mcp""/>
 </a>

","Star
 1.1k",2025-06-24 07:59:40.920526,mobile-mcp - MCP Server | Model Context Protocol Integration,"# Mobile Next - MCP server for Mobile Development and Automation  | iOS, Android, Simulator, Emulator, and physical devices

This is a [Model Context Protoco...","['mcp server', 'model context protocol', 'ai integration', 'mobile-mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mobile-mcp', 'description': '# Mobile Next - MCP server for Mobile Development and Automation  | iOS, Android, Simulator, Emulator, and physical devices\n\nThis is a [Model Context Protoco...', 'url': 'https://github.com/mobile-next/mobile-mcp', 'codeRepository': 'https://github.com/mobile-next/mobile-mcp', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Rust,Development,Time Management,,Workflows,"{
  ""mcpServers"": {
    ""mobile-mcp"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""@mobilenext/mobile-mcp@latest""]
    }
  }
}",,,,mobile-next,"This is a Model Context Protocol (MCP) server that enables scalable mobile automation, development through a platform-agnostic interface, eliminating the need for distinct iOS or Android knowledge. Yo..."
https://github.com/seekrays/mcp-monitor,seekrays/mcp-monitor,"# MCP System Monitor

[![Discord](https://img.shields.io/badge/Discord-Join%20Chat-blue?style=flat&logo=discord)](https://discord.gg/kbMJ9Qpf)

A system monitoring tool that exposes system metrics via the Model Context Protocol (MCP). This tool allows LLMs to retrieve real-time system information through an MCP-compatible interface.

![](./doc/snapshot-1.png)

## Features

This tool provides the following monitoring capabilities:

- **CPU Information**: Usage percentage, core count, and detailed CPU info
- **Memory Information**: Virtual and swap memory usage
- **Disk Information**: Disk usage, partitions, and I/O statistics
- **Network Information**: Network interfaces, connections, and traffic statistics
- **Host Information**: System details, uptime, boot time, and users
- **Process Information**: Process listing, sorting, and detailed per-process statistics


## Available Tools

### 1. CPU Information

```
Tool: get_cpu_info
Description: Get CPU information and usage
Parameters:
  - per_cpu (boolean, default: false): Whether to return data for each core
```

### 2. Memory Information

```
Tool: get_memory_info
Description: Get system memory usage information
Parameters: None
```

### 3. Disk Information

```
Tool: get_disk_info
Description: Get disk usage information
Parameters:
  - path (string, default: ""/""): Specify the disk path to query
  - all_partitions (boolean, default: false): Whether to return information for all partitions
```

### 4. Network Information

```
Tool: get_network_info
Description: Get network interface and traffic information
Parameters:
  - interface (string, optional): Specify the network interface name to query
```

### 5. Host Information

```
Tool: get_host_info
Description: Get host system information
Parameters: None
```

### 6. Process Information

```
Tool: get_process_info
Description: Get process information
Parameters:
  - pid (number, optional): Process ID to get detailed information for a specific process
  - limit (number, default: 10): Limit the number of processes returned
  - sort_by (string, default: ""cpu""): Sort field (cpu, memory, pid, name)
```


## Installation

```bash
git clone https://github.com/seekrays/mcp-monitor.git
cd mcp-monitor
make build
```

## Usage

Run the compiled binary:

```bash
./mcp-monitor
```

The server starts in stdio mode, ready to communicate with an MCP-compatible LLM client.


## Contributing

Contributions are welcome! Please feel free to submit a Pull Request. ","Star
 42",2025-06-24 07:59:40.920526,mcp-monitor - MCP Server | Model Context Protocol Integration,"# MCP System Monitor

[![Discord](https://img.shields.io/badge/Discord-Join%20Chat-blue?style=flat&logo=discord)](https://discord.gg/kbMJ9Qpf)

A system moni...","['mcp server', 'model context protocol', 'ai integration', 'mcp-monitor']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mcp-monitor', 'description': '# MCP System Monitor\n\n[![Discord](https://img.shields.io/badge/Discord-Join%20Chat-blue?style=flat&logo=discord)](https://discord.gg/kbMJ9Qpf)\n\nA system moni...', 'url': 'https://github.com/seekrays/mcp-monitor', 'codeRepository': 'https://github.com/seekrays/mcp-monitor', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",TypeScript,Automation,Time Management,,,,,,,seekrays,A system monitoring tool that exposes system metrics via the Model Context Protocol (MCP). This tool allows LLMs to retrieve real-time system information through an MCP-compatible interface.
https://github.com/jjsantos01/jupyter-notebook-mcp,jjsantos01/jupyter-notebook-mcp,"# JupyterMCP - Jupyter Notebook Model Context Protocol Integration

JupyterMCP connects [Jupyter Notebook](https://jupyter.org/) to [Claude AI](https://claude.ai/chat) through the Model Context Protocol (MCP), allowing Claude to directly interact with and control Jupyter Notebooks. This integration enables AI-assisted code execution, data analysis, visualization, and more.

## ⚠️ Compatibility Warning

**This tool is compatible ONLY with Jupyter Notebook version 6.x.**

It does NOT work with:

- Jupyter Lab
- Jupyter Notebook v7.x
- VS Code Notebooks
- Google Colab
- Any other notebook interfaces

## Features

- **Two-way communication**: Connect Claude AI to Jupyter Notebook through a WebSocket-based server
- **Cell manipulation**: Insert, execute, and manage notebook cells
- **Notebook management**: Save notebooks and retrieve notebook information
- **Cell execution**: Run specific cells or execute all cells in a notebook
- **Output retrieval**: Get output content from executed cells with text limitation options

## Components

The system consists of three main components:

1. **WebSocket Server (`jupyter_ws_server.py`)**: Sets up a WebSocket server inside Jupyter that bridges communication between notebook and external clients
2. **Client JavaScript (`client.js`)**: Runs in the notebook to handle operations (inserting cells, executing code, etc.)
3. **MCP Server (`jupyter_mcp_server.py`)**: Implements the Model Context Protocol and connects to the WebSocket server

## Installation

### Prerequisites

- [Python 3.12 or newer](https://www.python.org/downloads/) (probably also work with older versions, but not tested)
- [`uv` package manager](/README.md#installing-uv)
- [Claude AI desktop application](https://claude.ai/download)

#### Installing uv

If you're on Mac:

```bash
brew install uv
```

On Windows (PowerShell):

```bash
powershell -ExecutionPolicy ByPass -c ""irm https://astral.sh/uv/install.ps1 | iex""
```

For other platforms, see the [uv installation guide](https://docs.astral.sh/uv/getting-started/installation/).

### Setup

1. Clone or download this repository to your computer:

   ```bash
   git clone https://github.com/jjsantos01/jupyter-notebook-mcp.git
   ```

2. Create virtual environment with required packages an install `jupyter-mcp` kernel, so it can be recognized by your jupyter installation, if you had one before.

   ```bash
   uv run python -m ipykernel install --name jupyter-mcp
   ```

3. (optional) Install additional Python packages for your analysis:

   ```bash
   uv pip install seaborn
   ```

4. Configure Claude desktop integration:
   Go to `Claude` > `Settings` > `Developer` > `Edit Config` > `claude_desktop_config.json` to include the following:

   ```json
      {
       ""mcpServers"": {
           ""jupyter"": {
               ""command"": ""uv"",
               ""args"": [
                   ""--directory"",
                   ""/ABSOLUTE/PATH/TO/PARENT/REPO/FOLDER/src"",
                   ""run"",
                   ""jupyter_mcp_server.py""
               ]
           }
       }
   }
   ```

   Replace `/ABSOLUTE/PATH/TO/` with the actual path to the `src` folder on your system. For example:
   - Windows: `""C:\\Users\\<USER>\\GitHub\\jupyter-notebook-mcp\\src\\""`
   - Mac: `/Users/<USER>/GitHub/jupyter-notebook-mcp/src/`

   If you had previously opened Claude, then `File` > `Exit` and open it again.

## Usage

### Starting the Connection

1. Start your Jupyter Notebook (version 6.x) server:

   ```bash
   uv run jupyter nbclassic
   ```

2. Create a new Jupyter Notebook and make sure that you choose the `jupyter-mcp` kernel: `kernel` -> `change kernel` -> `jupyter-mcp`

3. In a notebook cell, run the following code to initialize the WebSocket server:

   ```python
   import sys
   sys.path.append('/path/to/jupyter-notebook-mcp/src')  # Add the path to where the scripts are located
   
   from jupyter_ws_server import setup_jupyter_mcp_integration
   
   # Start the WebSocket server inside Jupyter
   server, port = setup_jupyter_mcp_integration()
   ```

   Don't forget to replace here `'/path/to/jupyter-notebook-mcp/src'` with `src` folder on your system. For example:
   - Windows: `""C:\\Users\\<USER>\\GitHub\\jupyter-notebook-mcp\\src\\""`
   - Mac: `/Users/<USER>/GitHub/jupyter-notebook-mcp/src/`

   ![Notebook setup](/assets/img/notebook-setup.png)

4. Launch Claude desktop with MCP enabled.

### Using with Claude

Once connected, Claude will have access to the following tools:

- `ping` - Check server connectivity
- `insert_and_execute_cell` - Insert a cell at the specified position and execute it
- `save_notebook` - Save the current Jupyter notebook
- `get_cells_info` - Get information about all cells in the notebook
- `get_notebook_info` - Get information about the current notebook
- `run_cell` - Run a specific cell by its index
- `run_all_cells` - Run all cells in the notebook
- `get_cell_text_output` - Get the output content of a specific cell
- `get_image_output` - Get the images output of a specific cell
- `edit_cell_content` - Edit the content of an existing cell
- `set_slideshow_type`- Set the slide show type for cell

## ⚠️ DISCLAIMER

This is an experimental project and should be used with caution. This tool runs arbitrary Python code in your computer, which could potentially modify or delete data if not used carefully. Always back up your important projects and data.

## Example Prompts

Ask Claude to perform notebook operations:

### Python example

You can check the [example notebook](/notebooks/example_notebook.ipynb) and the [video demo](https://x.com/jjsantoso/status/1906780778807390562)

```plain
You have access to a Jupyter Notebook server.

I need to create a presentation about Python's Seaborn library.  
The content is as follows:

- What is Seaborn?
- Long vs. Wide data format
- Advantages of Seaborn over Matplotlib
- Commonly used Seaborn functions
- Live demonstration (comparison of Seaborn vs. Matplotlib)
  - Bar plot
  - Line plot
  - Scatter plot

For each concept, I want the main explanations provided in markdown cells, followed by one or more Python code cells demonstrating its usage. Keep the text concise—the cells shouldn't exceed 10 lines each.

Use appropriate slideshow types for each cell to make the presentation visually appealing.
```

[Check Here the full conversation](https://claude.ai/share/420b6aa6-b84b-437f-a6a6-89d310c36d52)

### Stata example

For this example, you need the [Stata Software](https://www.stata.com/) (v17 or later), which is not open source. If you already have Stata, you need to install the [`stata-setup`](https://pypi.org/project/stata-setup/) package:

```bash
uv pip install stata-setup
```

Then, at the begining of your notebook, you need to additionally include:

```python
import stata_setup
stata_setup.config('your_stata_installation_directory', 'your_stata_edition')
```

You can check the [example notebook](/notebooks/stata_example.ipynb) and the [video demo](https://x.com/jjsantoso/status/1906780784800731251)

This exercise comes from [Professor John Robert Warren webpage](https://www.rob-warren.com/soc3811_stata_exercises.html)

```plain
You have access to a Jupyter Notebook server. By default it runs Python, but you can run Stata (v18) code in this server using the %%stata magic, for example:

%%stata
display ""hello world""

Run the available tools to solve the exercise, execute the code, and interpret the results.

**EXERCISE:**

In this exercise, you will use data from the American Community Survey (ACS). The ACS is a product of the U.S. Census Bureau and involves interviewing millions of Americans each year. For an introduction to the ACS, visit the ACS website (here).

For this exercise, I have created a data file containing two variables collected from respondents of the 2010 ACS who lived in one of two metropolitan areas: Minneapolis/St Paul and Duluth/Superior. The two variables are: (1) People's poverty status and (2) the time it takes people to commute to work.

Use STATA syntax files you already have (from the first assignment or class examples) and modify them to accomplish the following goals.

1. Read the data file (`""./stata_assignment_2.dat""`) for this assignment into STATA.
2. Be sure to declare ""zero"" as a missing value for `TRANTIME`, the commuting time variable.
3. Create a new dichotomous poverty variable that equals ""1"" if a person's income-to-poverty-line ratio (`POVRATIO`) is less than 100, and ""0"" otherwise; see the bottom of the assignment for an example of how to do this in STATA.
4. Separately for Minneapolis/St Paul and Duluth/Superior, produce:
   - a histogram of the commuting time (`TRANTIME`) variable.
   - measures of central tendency and spread for commuting time.
   - a frequency distribution for the poverty status (0 vs 1) variable.
5. Separately for Minneapolis/St Paul and Duluth/Superior, use STATA code to produce:
   - a 95% confidence interval for the mean commuting time.
   - a 95% confidence interval for the proportion of people who are poor. See below for an example of how to do this in STATA.

Use the results from step #4 above to:

6. Separately for Minneapolis/St Paul and Duluth/Superior, manually calculate:
   - a 95% confidence interval for the mean commuting time.
   - a 95% confidence interval for the proportion of people who are poor.
7. Confirm that your answers from steps #5 and #6 match.

Based on the results above, answer this question:

8. How do you interpret the confidence intervals calculated in steps #5 and #6 above?

9. Finally, create a do file (.do) with the all the Stata code and the answers as comments.

---

**DESCRIPTION OF VARIABLES IN ""STATA ASSIGNMENT 2.DAT""**

**METAREAD** (Column 4-7)  
Metropolitan Area  
- `2240`: Duluth-Superior, MN/WI  
- `5120`: Minneapolis-St. Paul, MN  

**POVRATIO** (Column 18-20)  
Ratio of person's income to the poverty threshold:  
- `<100`: Below Poverty Line  
- `100`: At Poverty Line  
- `>100`: Above Poverty Line  

**TRANTIME** (Column 21-23)  
Travel time to work  
- `0`: Zero minutes  
- `1`: 1 Minute  
- etc.

```

[Check Here the full conversation](https://claude.ai/share/97b5a546-9375-434d-8224-561706782880)

## Testing with External Client

You can test the functionality without using Claude Desktop with the included external client:

```bash
uv run python src/jupyter_ws_external_client.py
```

This will provide an interactive menu to test some available functions.

For automated testing of all commands:

```bash
uv run python src/jupyter_ws_external_client.py --batch
```

## Troubleshooting

- **Connection Issues**: If you experience connection timeouts, the client includes a reconnection mechanism. You can also try restarting the WebSocket server.
- **Cell Execution Problems**: If cell execution doesn't work, check that the cell content is valid Python/Markdown and that the notebook kernel is running.
- **WebSocket Port Conflicts**: If the default port (8765) is already in use, the server will automatically try to find an available port.

## Limitations

- Only supports Jupyter Notebook 6.x
- Text output from cells is limited to 1500 characters by default
- Does not support advanced Jupyter widget interactions
- Connection may timeout after periods of inactivity

## License

[MIT](/LICENSE)

## Other Jupyter MCPs

This project is inspired by similar MCP integrations for Jupyter as:

- [ihrpr](https://github.com/ihrpr/mcp-server-jupyter)
- [Datalayer](https://github.com/datalayer/jupyter-mcp-server/tree/main)
","Star
 74",2025-06-24 07:59:40.920526,jupyter-notebook-mcp - MCP Server | Model Context Protocol Integration,"# JupyterMCP - Jupyter Notebook Model Context Protocol Integration

JupyterMCP connects [Jupyter Notebook](https://jupyter.org/) to [Claude AI](https://claud...","['mcp server', 'model context protocol', 'ai integration', 'jupyter-notebook-mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'jupyter-notebook-mcp', 'description': '# JupyterMCP - Jupyter Notebook Model Context Protocol Integration\n\nJupyterMCP connects [Jupyter Notebook](https://jupyter.org/) to [Claude AI](https://claud...', 'url': 'https://github.com/jjsantos01/jupyter-notebook-mcp', 'codeRepository': 'https://github.com/jjsantos01/jupyter-notebook-mcp', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Python,AI Tools,Time Management,pip install seaborn | pip install stata-setup,"Prerequisites, Setup, edit_cell_content, get_cell_text_output, get_cells_info, get_image_output, get_notebook_info, insert_and_execute_cell, ping, run_all_cells","{
       ""mcpServers"": {
           ""jupyter"": {
               ""command"": ""uv"",
               ""args"": [
                   ""--directory"",
                   ""/ABSOLUTE/PATH/TO/PARENT/REPO/FOLDER/src"",
                   ""run"",
                   ""jupyter_mcp_server.py""
               ]
           }
       }
   }
   ```

   Replace `/ABSOLUTE/PATH/TO/` with the actual path to the `src` folder on your system. For example:
   - Windows: `""C:\\Users\\<USER>\\GitHub\\jupyter-notebook-mcp\\src\\""`
 ","seaborn, stata-setup",MIT,,jjsantos01,"JupyterMCP connects Jupyter Notebook to Claude AI through the Model Context Protocol (MCP), allowing Claude to directly interact with and control Jupyter Notebooks. This integration enables AI-assiste..."
https://github.com/scrapeless-ai/scrapeless-mcp-server,scrapeless-ai/scrapeless-mcp-server,"![preview](./banner.png)

# Scrapeless Mcp Server

Model Context Protocol (MCP) is an open protocol that enables seamless integration between LLM applications and external data sources and tools. MCP provides a standardized way to connect LLM with the required context, helping you efficiently enhance chat interfaces, build AI-driven IDEs, or create custom AI workflows.

Seamlessly integrate real-time Google SERP(Google Search, Google Flight, Google Map, Google Jobs....) results into your LLM applications using the Scrapeless MCP server. This server acts as a bridge between LLMs (like ChatGPT, Claude, etc.) and Scrapeless's Google SERP, enabling dynamic context retrieval for AI workflows, chatbots, and research tools.

👉 Live MCP Endpoint: 
- [mcp.so](https://mcp.so/server/scrapelessMcpServer/scrapeless-ai)
- [glama.ai](https://glama.ai/mcp/servers/@scrapeless-ai/scrapeless-mcp-server)

📦 NPM Package: [scrapeless-mcp-server](https://www.npmjs.com/package/scrapeless-mcp-server)

## Overview

This project provides several MCP servers that enable AI assistants like Claude to perform various search operations and retrieve data from:

- Google Search

## Tools

### 1. Search Tool
- Name: `google-search`
- Description: Search the web using Scrapeless
- Parameters:
    * `query` (required): Parameter defines the query you want to search. You can use anything that you would use in a regular Google search. e.g. inurl:, site:, intitle:.
    * `gl` (optional, default: ""us""): Parameter defines the country to use for the Google search. It's a two-letter country code. (e.g., us for the United States, uk for United Kingdom, or fr for France).
    * `hl` (optional, default: ""en""): Parameter defines the language to use for the Google search. It's a two-letter language code. (e.g., en for English, es for Spanish, or fr for French).


## Setup Guide

### 1. Get Scrapeless Key
1. Register at [Scrapeless](https://app.scrapeless.com/passport/register?utm_source=github&utm_medium=mcp)
2. [Get your free trial](https://app.scrapeless.com/landing/guide?utm_source=github&utm_medium=mcp)
3. [Generate API Key](https://app.scrapeless.com/dashboard/settings/api-key?utm_source=github&utm_medium=mcp)


### 2. Configure

```json
{
  ""mcpServers"": {
    ""scrapelessMcpServer"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""scrapeless-mcp-server""],
      ""env"": {
        ""SCRAPELESS_KEY"": ""YOUR_SCRAPELESS_KEY""
      }
    }
  }
}
```


## Example Queries

Here are some examples of how to use these servers with Claude Desktop:

### Google Search
```
Please search for ""climate change solutions"" and summarize the top results.
```


## Installation

### Prerequisites

- Node.js 22 or higher
- NPM or Yarn

### Install from Source

1. Clone the repository:
```bash
git clone https://github.com/scrapeless-ai/scrapeless-mcp-server.git
cd scrapeless-mcp-server
```

2. Install dependencies:
```bash
npm install
```


3. Build the server:
```bash
npm run build
```


## Community
- [MCP Server Discord](https://backend.scrapeless.com/app/api/v1/public/links/discord)","Star
 27",2025-06-24 07:59:40.920526,scrapeless-mcp-server - MCP Server | Model Context Protocol Integration,"![preview](./banner.png)

# Scrapeless Mcp Server

Model Context Protocol (MCP) is an open protocol that enables seamless integration between LLM application...","['mcp server', 'model context protocol', 'ai integration', 'scrapeless-mcp-server']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'scrapeless-mcp-server', 'description': '![preview](./banner.png)\n\n# Scrapeless Mcp Server\n\nModel Context Protocol (MCP) is an open protocol that enables seamless integration between LLM application...', 'url': 'https://github.com/scrapeless-ai/scrapeless-mcp-server', 'codeRepository': 'https://github.com/scrapeless-ai/scrapeless-mcp-server', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Rust,AI Tools,Search Engine,npm install,Prerequisites,"{
  ""mcpServers"": {
    ""scrapelessMcpServer"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""scrapeless-mcp-server""],
      ""env"": {
        ""SCRAPELESS_KEY"": ""YOUR_SCRAPELESS_KEY""
      }
    }
  }
}",,,,scrapeless-ai,Model Context Protocol (MCP) is an open protocol that enables seamless integration between LLM applications and external data sources and tools. MCP provides a standardized way to connect LLM with the...
https://github.com/KyrieTangSheng/mcp-server-nationalparks,KyrieTangSheng/mcp-server-nationalparks,"# National Parks MCP Server
[![smithery badge](https://smithery.ai/badge/@KyrieTangSheng/mcp-server-nationalparks)](https://smithery.ai/server/@KyrieTangSheng/mcp-server-nationalparks)
[![Verified on MseeP](https://mseep.ai/badge.svg)](https://mseep.ai/app/8c07fa61-fd4b-4662-8356-908408e45e44)

MCP Server for the National Park Service (NPS) API, providing real-time information about U.S. National Parks, including park details, alerts, and activities.

## Tools

1. `findParks`
   - Search for national parks based on various criteria
   - Inputs:
     - `stateCode` (optional string): Filter parks by state code (e.g., ""CA"" for California). Multiple states can be comma-separated (e.g., ""CA,OR,WA"")
     - `q` (optional string): Search term to filter parks by name or description
     - `limit` (optional number): Maximum number of parks to return (default: 10, max: 50)
     - `start` (optional number): Start position for results (useful for pagination)
     - `activities` (optional string): Filter by available activities (e.g., ""hiking,camping"")
   - Returns: Matching parks with detailed information

2. `getParkDetails`
   - Get comprehensive information about a specific national park
   - Inputs:
     - `parkCode` (string): The park code of the national park (e.g., ""yose"" for Yosemite, ""grca"" for Grand Canyon)
   - Returns: Detailed park information including descriptions, hours, fees, contacts, and activities

3. `getAlerts`
   - Get current alerts for national parks including closures, hazards, and important information
   - Inputs:
     - `parkCode` (optional string): Filter alerts by park code (e.g., ""yose"" for Yosemite). Multiple parks can be comma-separated (e.g., ""yose,grca"")
     - `limit` (optional number): Maximum number of alerts to return (default: 10, max: 50)
     - `start` (optional number): Start position for results (useful for pagination)
     - `q` (optional string): Search term to filter alerts by title or description
   - Returns: Current alerts organized by park

4. `getVisitorCenters`
   - Get information about visitor centers and their operating hours
   - Inputs:
     - `parkCode` (optional string): Filter visitor centers by park code (e.g., ""yose"" for Yosemite). Multiple parks can be comma-separated (e.g., ""yose,grca"")
     - `limit` (optional number): Maximum number of visitor centers to return (default: 10, max: 50)
     - `start` (optional number): Start position for results (useful for pagination)
     - `q` (optional string): Search term to filter visitor centers by name or description
   - Returns: Visitor center information including location, hours, and contact details

5. `getCampgrounds`
   - Get information about available campgrounds and their amenities
   - Inputs:
     - `parkCode` (optional string): Filter campgrounds by park code (e.g., ""yose"" for Yosemite). Multiple parks can be comma-separated (e.g., ""yose,grca"")
     - `limit` (optional number): Maximum number of campgrounds to return (default: 10, max: 50)
     - `start` (optional number): Start position for results (useful for pagination)
     - `q` (optional string): Search term to filter campgrounds by name or description
   - Returns: Campground information including amenities, fees, and reservation details

6. `getEvents`
   - Find upcoming events at parks
   - Inputs:
     - `parkCode` (optional string): Filter events by park code (e.g., ""yose"" for Yosemite). Multiple parks can be comma-separated (e.g., ""yose,grca"")
     - `limit` (optional number): Maximum number of events to return (default: 10, max: 50)
     - `start` (optional number): Start position for results (useful for pagination)
     - `dateStart` (optional string): Start date for filtering events (format: YYYY-MM-DD)
     - `dateEnd` (optional string): End date for filtering events (format: YYYY-MM-DD)
     - `q` (optional string): Search term to filter events by title or description
   - Returns: Event information including dates, times, and descriptions

## Setup

### Installing via Smithery

To install mcp-server-nationalparks for Claude Desktop automatically via [Smithery](https://smithery.ai/server/@KyrieTangSheng/mcp-server-nationalparks):

```bash
npx -y @smithery/cli install @KyrieTangSheng/mcp-server-nationalparks --client claude
```

### NPS API Key
1. Get a free API key from the [National Park Service Developer Portal](https://www.nps.gov/subjects/developer/get-started.htm)
2. Store this key securely as it will be used to authenticate requests

### Usage with Claude Desktop

To use this server with Claude Desktop, add the following to your `claude_desktop_config.json`:

```json
{
  ""mcpServers"": {
    ""nationalparks"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""mcp-server-nationalparks""],
      ""env"": {
        ""NPS_API_KEY"": ""YOUR_NPS_API_KEY""
      }
    }
  }
}
```
## Example Usage

### Finding Parks in a State
```
Tell me about national parks in Colorado.
```

### Getting Details About a Specific Park
```
What's the entrance fee for Yellowstone National Park?
```

### Checking for Alerts or Closures
```
Are there any closures or alerts at Yosemite right now?
```

### Finding Visitor Centers
```
What visitor centers are available at Grand Canyon National Park?
```

### Looking for Campgrounds
```
Are there any campgrounds with electrical hookups in Zion National Park?
```

### Finding Upcoming Events
```
What events are happening at Acadia National Park next weekend?
```

### Planning a Trip Based on Activities
```
Which national parks in Utah have good hiking trails?
```

## License

This MCP server is licensed under the MIT License. See the LICENSE file for details.


## Appendix: Popular National Parks and their codes

| Park Name | Park Code |
|-----------|-----------|
| Yosemite | yose |
| Grand Canyon | grca |
| Yellowstone | yell |
| Zion | zion |
| Great Smoky Mountains | grsm |
| Acadia | acad |
| Olympic | olym |
| Rocky Mountain | romo |
| Joshua Tree | jotr |
| Sequoia & Kings Canyon | seki |

For a complete list, visit the [NPS website](https://www.nps.gov/findapark/index.htm).
","Star
 18",2025-06-24 07:59:40.920526,mcp-server-nationalparks - MCP Server | Model Context Protocol Integration,"# National Parks MCP Server
[![smithery badge](https://smithery.ai/badge/@KyrieTangSheng/mcp-server-nationalparks)](https://smithery.ai/server/@KyrieTangShen...","['mcp server', 'model context protocol', 'ai integration', 'mcp-server-nationalparks']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mcp-server-nationalparks', 'description': '# National Parks MCP Server\n[![smithery badge](https://smithery.ai/badge/@KyrieTangSheng/mcp-server-nationalparks)](https://smithery.ai/server/@KyrieTangShen...', 'url': 'https://github.com/KyrieTangSheng/mcp-server-nationalparks', 'codeRepository': 'https://github.com/KyrieTangSheng/mcp-server-nationalparks', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Go,AI Tools,Search Engine,,"CA, OR, contacts, fees, hazards, hiking, hours, times, yose","{
  ""mcpServers"": {
    ""nationalparks"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""mcp-server-nationalparks""],
      ""env"": {
        ""NPS_API_KEY"": ""YOUR_NPS_API_KEY""
      }
    }
  }
}",,MIT,,KyrieTangSheng,"MCP Server for the National Park Service (NPS) API, providing real-time information about U.S. National Parks, including park details, alerts, and activities."
https://github.com/IvanMurzak/Unity-MCP,IvanMurzak/Unity-MCP,"# Unity MCP (Server + Plugin)

[![openupm](https://img.shields.io/npm/v/com.ivanmurzak.unity.mcp?label=openupm&registry_uri=https://package.openupm.com)](https://openupm.com/packages/com.ivanmurzak.unity.mcp/) ![License](https://img.shields.io/github/license/IvanMurzak/Unity-MCP) [![Stand With Ukraine](https://raw.githubusercontent.com/vshymanskyy/StandWithUkraine/main/badges/StandWithUkraine.svg)](https://stand-with-ukraine.pp.ua)

![image](https://github.com/user-attachments/assets/8f595879-a578-421a-a06d-8c194af874f7)

| Unity Version | Editmode | Playmode | Standalone |
|---------------|----------|----------|------------|
| 2022.3.61f1   | ![2022.3.61f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/2022.3.61f1_editmode.yml?label=2022.3.61f1-editmode) | ![2022.3.61f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/2022.3.61f1_playmode.yml?label=2022.3.61f1-playmode) | ![2022.3.61f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/2022.3.61f1_standalone.yml?label=2022.3.61f1-standalone) |
| 2023.2.20f1   | ![2023.2.20f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/2023.2.20f1_editmode.yml?label=2023.2.20f1-editmode) | ![2023.2.20f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/2023.2.20f1_playmode.yml?label=2023.2.20f1-playmode) | ![2023.2.20f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/2023.2.20f1_standalone.yml?label=2023.2.20f1-standalone) |
| 6000.0.46f1   | ![6000.0.46f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/6000.0.46f1_editmode.yml?label=6000.0.46f1-editmode) | ![6000.0.46f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/6000.0.46f1_playmode.yml?label=6000.0.46f1-playmode) | ![6000.0.46f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/6000.0.46f1_standalone.yml?label=6000.0.46f1-standalone) |

**[Unity-MCP](https://github.com/IvanMurzak/Unity-MCP)** is a bridge between LLM and Unity. It exposes and explains to LLM Unity's tools. LLM understands the interface and utilizes the tools in the way a user asks.

Connect **[Unity-MCP](https://github.com/IvanMurzak/Unity-MCP)** to LLM client such as [Claude](https://claude.ai/download) or [Cursor](https://www.cursor.com/) using integrated `AI Connector` window. Custom clients are supported as well.

The project is designed to let developers to add custom tools soon. After that the next goal is to enable the same features in player's build. For not it works only in Unity Editor.

The system is extensible: you can define custom `tool`s directly in your Unity project codebase, exposing new capabilities to the AI or automation clients. This makes Unity-MCP a flexible foundation for building advanced workflows, rapid prototyping, or integrating AI-driven features into your development process.

## AI Tools

<table>
<tr>
<td valign=""top"">

### GameObject

- ✅ Create
- ✅ Destroy
- ✅ Find
- ✅ Modify (tag, layer, name, static)
- ✅ Set parent
- ✅ Duplicate

##### GameObject.Components

- ✅ Add Component
- ✅ Get Components
- ✅ Modify Component
- - ✅ `Field` set value
- - ✅ `Property` set value
- - ✅ `Reference` link set
- ✅ Destroy Component
- 🔲 Remove missing components

### Editor

- ✅ State (Playmode)
  - ✅ Get
  - ✅ Set
- ✅ Get Windows
- ✅ Layer
  - ✅ Get All
  - ✅ Add
  - ✅ Remove
- ✅ Tag
  - ✅ Get All
  - ✅ Add
  - ✅ Remove
- ✅ Execute `MenuItem`
- 🔲 Run Tests

#### Editor.Selection

- ✅ Get selection
- ✅ Set selection

### Prefabs

- ✅ Instantiate
- 🔲 Create
- ✅ Open
- ✅ Modify (GameObject.Modify)
- ✅ Save
- ✅ Close

### Package

- 🔲 Get installed
- 🔲 Install
- 🔲 Remove
- 🔲 Update

</td>
<td valign=""top"">

### Assets

- ✅ Create
- ✅ Find
- ✅ Refresh
- ✅ Read
- ✅ Modify
- ✅ Rename
- ✅ Delete
- ✅ Move
- ✅ Create folder

### Scene

- ✅ Create
- ✅ Save
- ✅ Load
- ✅ Unload
- ✅ Get Loaded
- ✅ Get hierarchy
- 🔲 Search (editor)
- 🔲 Raycast (understand volume)

### Materials

- ✅ Create
- ✅ Modify (Assets.Modify)
- ✅ Read (Assets.Read)
- ✅ Assign to a Component on a GameObject

### Shader

- ✅ List All

### Scripts

- ✅ Read
- ✅ Update or Create
- ✅ Delete

### Scriptable Object

- 🔲 Create
- 🔲 Read
- 🔲 Modify
- 🔲 Remove

### Debug

- 🔲 Read logs (console)

### Component

- ✅ Get All

</td>
</tr>
</table>

> **Legend:**
> ✅ = Implemented & available, 🔲 = Planned / Not yet implemented

# Installation

1. [Install .NET 9.0](https://dotnet.microsoft.com/en-us/download)
2. [Install OpenUPM-CLI](https://github.com/openupm/openupm-cli#installation)

- Open command line in Unity project folder
- Run the command

```bash
openupm add com.ivanmurzak.unity.mcp
```

# Usage

1. Make sure your project path doesn't have a space symbol "" "".
> - ✅ `C:/MyProjects/Project`
> - ❌ `C:/My Projects/Project`

2. Open Unity project, go 👉 `Window/AI Connector (Unity-MCP)`.

![Unity_WaSRb5FIAR](https://github.com/user-attachments/assets/e8049620-6614-45f1-92d7-cc5d00a6b074)

3. Install MCP client
> - [Install Cursor](https://www.cursor.com/) (recommended)
> - [Install Claude](https://claude.ai/download)

4. Sign-in into MCP client
5. Click `Configure` at your MCP client.

![image](https://github.com/user-attachments/assets/19f80179-c5b3-4e9c-bdf6-07edfb773018)

6. Restart your MCP client.
7. Make sure `AI Connector` is ""Connected"" or ""Connecting..."" after restart.
8. Test AI connection in your Client (Cursor, Claude Desktop). Type any question or task into the chat. Something like:

  ```text
  Explain my scene hierarchy
  ```

# Add custom `tool`

> ⚠️ It only works with MCP client that supports dynamic tool list update.

Unity-MCP is designed to support custom `tool` development by project owner. MCP server takes data from Unity plugin and exposes it to a Client. So anyone in the MCP communication chain would receive the information about a new `tool`. Which LLM may decide to call at some point.

To add a custom `tool` you need:

1. To have a class with attribute `McpPluginToolType`.
2. To have a method in the class with attribute `McpPluginTool`.
3. [optional] Add `Description` attribute to each method argument to let LLM to understand it.
4. [optional] Use `string? optional = null` properties with `?` and default value to mark them as `optional` for LLM.

> Take a look that the line `MainThread.Instance.Run(() =>` it allows to run the code in Main thread which is needed to interact with Unity API. If you don't need it and running the tool in background thread is fine for the tool, don't use Main thread for efficiency purpose.

```csharp
[McpPluginToolType]
public class Tool_GameObject
{
    [McpPluginTool
    (
        ""MyCustomTask"",
        Title = ""Create a new GameObject""
    )]
    [Description(""Explain here to LLM what is this, when it should be called."")]
    public string CustomTask
    (
        [Description(""Explain to LLM what is this."")]
        string inputData
    )
    {
        // do anything in background thread

        return MainThread.Instance.Run(() =>
        {
            // do something in main thread if needed

            return $""[Success] Operation completed."";
        });
    }
}
```

# Add custom in-game `tool`

> ⚠️ Not yet supported. The work is in progress


# Contribution

Feel free to add new `tool` into the project.

1. Fork the project.
2. Implement new `tool` in your forked repository.
3. Create Pull Request into original [Unity-MCP](https://github.com/IvanMurzak/Unity-MCP) repository.
","Star
 215",2025-06-24 07:59:40.920526,Unity-MCP - MCP Server | Model Context Protocol Integration,"# Unity MCP (Server + Plugin)

[![openupm](https://img.shields.io/npm/v/com.ivanmurzak.unity.mcp?label=openupm&registry_uri=https://package.openupm.com)](htt...","['mcp server', 'model context protocol', 'ai integration', 'Unity-MCP']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'Unity-MCP', 'description': '# Unity MCP (Server + Plugin)\n\n[![openupm](https://img.shields.io/npm/v/com.ivanmurzak.unity.mcp?label=openupm&registry_uri=https://package.openupm.com)](htt...', 'url': 'https://github.com/IvanMurzak/Unity-MCP', 'codeRepository': 'https://github.com/IvanMurzak/Unity-MCP', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",C#,AI Tools,Gaming,,"Assets, Component, Debug, Editor, GameObject, Materials, Package, Prefabs, Scene, Scripts",,label,,,IvanMurzak,| Unity Version | Editmode | Playmode | Standalone |
https://github.com/browsermcp/mcp,browsermcp/mcp,"<a href=""https://browsermcp.io"">
  <img src=""./.github/images/banner.png"" alt=""Browser MCP banner"">
</a>

<h3 align=""center"">Browser MCP</h3>

<p align=""center"">
  Automate your browser with AI.
  <br />
  <a href=""https://browsermcp.io""><strong>Website</strong></a> 
  •
  <a href=""https://docs.browsermcp.io""><strong>Docs</strong></a>
</p>

## About

Browser MCP is an MCP server + Chrome extension that allows you to automate your browser using AI applications like VS Code, Claude, Cursor, and Windsurf.

## Features

- ⚡ Fast: Automation happens locally on your machine, resulting in better performance without network latency.
- 🔒 Private: Since automation happens locally, your browser activity stays on your device and isn't sent to remote servers.
- 👤 Logged In: Uses your existing browser profile, keeping you logged into all your services.
- 🥷🏼 Stealth: Avoids basic bot detection and CAPTCHAs by using your real browser fingerprint.

## Contributing

This repo contains all the core MCP code for Browser MCP, but currently cannot yet be built on its own due to dependencies on utils and types from the monorepo where it's developed.

## Credits

Browser MCP was adapted from the [Playwright MCP server](https://github.com/microsoft/playwright-mcp) in order to automate the user's browser rather than creating new browser instances. This allows using the user's existing browser profile to use logged-in sessions and avoid bot detection mechanisms that commonly block automated browser use.
","Star
 2.5k",2025-06-24 07:57:58.098202,mcp - MCP Server | Model Context Protocol Integration,"<a href=""https://browsermcp.io"">
  <img src=""./.github/images/banner.png"" alt=""Browser MCP banner"">
</a>

<h3 align=""center"">Browser MCP</h3>

<p align=""cent...","['mcp server', 'model context protocol', 'ai integration', 'mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mcp', 'description': '<a href=""https://browsermcp.io"">\n  <img src=""./.github/images/banner.png"" alt=""Browser MCP banner"">\n</a>\n\n<h3 align=""center"">Browser MCP</h3>\n\n<p align=""cent...', 'url': 'https://github.com/browsermcp/mcp', 'codeRepository': 'https://github.com/browsermcp/mcp', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",C#,Development,Browser Automation,,,,,,,browsermcp,"<a href=""https://browsermcp.io"">"
https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server,hannesrudolph/imessage-query-fastmcp-mcp-server,"[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/hannesrudolph-imessage-query-fastmcp-mcp-server-badge.png)](https://mseep.ai/app/hannesrudolph-imessage-query-fastmcp-mcp-server)

# iMessage Query MCP Server

An MCP server that provides safe access to your iMessage database through Model Context Protocol (MCP). This server is built with the FastMCP framework and the imessagedb library, enabling LLMs to query and analyze iMessage conversations with proper phone number validation and automatic macOS permission handling.

## 📋 System Requirements

- macOS (required for iMessage database access)
- Python 3.12+ (required for modern type hints)
- **uv** (modern Python package manager)
- **Full Disk Access permission** for your MCP client (Claude Desktop, Cursor, VS Code, etc.)

## 📦 Dependencies

### Install uv (Required)

This project uses `uv` for fast, reliable Python package management. Install it first:

```bash
# Install uv using Homebrew (recommended)
brew install uv

# Or install using the official installer
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### Python Dependencies

The script automatically manages its dependencies using the embedded metadata. No separate installation needed! Dependencies include:

- **fastmcp**: Framework for building Model Context Protocol servers
- **imessagedb**: Python library for accessing and querying the macOS Messages database
- **phonenumbers**: Google's phone number handling library for proper number validation and formatting

All dependencies are automatically installed when the script runs via `uv`.

## 📑 Table of Contents
- [System Requirements](#-system-requirements)
- [Dependencies](#-dependencies)
- [MCP Tools](#%EF%B8%8F-mcp-tools)
- [Getting Started](#-getting-started)
- [Installation Options](#-installation-options)
  - [Claude Desktop](#option-1-install-for-claude-desktop)
  - [Cline VSCode Plugin](#option-2-install-for-cline-vscode-plugin)
- [macOS Permissions Setup](#-macos-permissions-setup)
- [Safety Features](#-safety-features)
- [Development Documentation](#-development-documentation)
- [Environment Variables](#%EF%B8%8F-environment-variables)

## 🛠️ MCP Tools

The server exposes the following tools to LLMs:

### get_chat_transcript
Retrieve message history for a specific phone number with optional date filtering.

**Parameters:**
- `phone_number` (required): Phone number in any format (E.164 format preferred)
- `start_date` (optional): Start date in ISO format (YYYY-MM-DD)
- `end_date` (optional): End date in ISO format (YYYY-MM-DD)

**Features:**
- Automatic phone number validation and formatting
- Message text and timestamps
- Attachment information with missing file detection
- Date range filtering (defaults to last 7 days if no dates specified)
- Sender identification (is_from_me flag)

## 🚀 Getting Started

Clone the repository:

```bash
git clone https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server.git
cd imessage-query-fastmcp-mcp-server
```

## 📦 Installation Options

You can install this MCP server in Claude Desktop, Cline VSCode plugin, or any other MCP client. Choose the option that best suits your needs.

### Option 1: Claude Desktop

1. **Find your Claude Desktop config file:**
   - **Location**: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - Create the file if it doesn't exist

2. **Add the server configuration:**

```json
{
  ""mcpServers"": {
    ""imessage-query"": {
      ""command"": ""/full/path/to/imessage-query-server.py""
    }
  }
}
```

3. **Replace the path** with the full path to your cloned repository (e.g., `/Users/<USER>/Projects/imessage-query-fastmcp-mcp-server/imessage-query-server.py`)

4. **Restart Claude Desktop** completely (Cmd+Q, then relaunch)

### Option 2: Cline VSCode Plugin

To use this server with the [Cline VSCode plugin](http://cline.bot):

1. In VSCode, click the server icon (☰) in the Cline plugin sidebar
2. Click the ""Edit MCP Settings"" button (✎)
3. Add the following configuration to the settings file:

```json
{
  ""imessage-query"": {
    ""command"": ""/full/path/to/imessage-query-server.py""
  }
}
```

4. **Replace the path** with the full path to your cloned repository

### Option 3: Other MCP Clients

For other MCP clients, use the direct script path as the command:

```
/full/path/to/imessage-query-server.py
```

The script's shebang (`#!/usr/bin/env -S uv run --script`) handles dependency management automatically.

> **Note**: This simplified configuration replaces the previous FastMCP installation method. The script is now self-contained and manages its own dependencies through `uv`.

## 🔐 macOS Permissions Setup

This server requires **Full Disk Access** permission to read the iMessage database. The server includes intelligent permission detection and will guide you through the setup process.

### Automatic Permission Detection

When you first use the server, it will:
1. **Detect your MCP client** (Claude Desktop, Cursor, VS Code, etc.)
2. **Check for Full Disk Access** permission
3. **Automatically open System Preferences** to the correct settings panel
4. **Provide step-by-step instructions** specific to your application

### Manual Permission Setup

If automatic detection doesn't work, follow these steps:

1. **Open System Preferences** → **Privacy & Security** → **Full Disk Access**
2. **Click the lock icon** and enter your password to make changes
3. **Click the '+' button** to add an application
4. **Navigate to and select your MCP client:**
   - **Claude Desktop**: `/Applications/Claude.app`
   - **Cursor**: `/Applications/Cursor.app`
   - **VS Code**: `/Applications/Visual Studio Code.app`
5. **Restart your MCP client** completely (Cmd+Q, then relaunch)

### Common Issues

- **Permission denied errors**: Make sure you've restarted your MCP client after granting permission
- **""uv"" instead of app name**: The server will auto-detect your actual MCP client and provide correct instructions
- **Database not found**: Ensure you've used the Messages app and iMessage is enabled

### Security Note

This server only requires **read access** to your iMessage database. It cannot modify, delete, or send messages.

## 🔒 Safety Features

- **Read-only access** to the iMessage database (cannot modify, delete, or send messages)
- **Phone number validation** using Google's phonenumbers library with proper E.164 formatting
- **Safe attachment handling** with missing file detection and metadata extraction
- **Date range validation** to prevent invalid queries
- **Progress output suppression** for clean JSON responses in MCP protocol
- **Intelligent permission detection** with automatic System Preferences navigation
- **MCP client identification** for accurate permission guidance

## 📚 Development Documentation

The repository includes comprehensive documentation for development:

- `dev_docs/imessagedb-documentation.txt`: Complete documentation about the iMessage database structure and the imessagedb library's capabilities
- `dev_docs/fastmcp-documentation.txt`: FastMCP framework details and MCP tool development
- `dev_docs/mcp-documentation.txt`: Model Context Protocol specification

This documentation serves as context when developing features and can be used with LLMs to assist in development.

## ⚙️ Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SQLITE_DB_PATH` | Custom path to iMessage database | `~/Library/Messages/chat.db` |

The server automatically locates the iMessage database in the default macOS location. The environment variable is only needed for custom database locations.

## 🔧 Advanced Usage

### Custom Database Path

If you need to use a custom database path:

```bash
export SQLITE_DB_PATH=""/path/to/custom/chat.db""
```

### Testing the Server

Test the server directly using mcptools (github.com/f/mcptools):

```bash
# Navigate to the repository directory
cd /path/to/imessage-query-fastmcp-mcp-server

# List available tools
mcp tools ./imessage-query-server.py

# Test a tool call
mcp call get_chat_transcript ./imessage-query-server.py -p '{""phone_number"": ""+1234567890""}'
```

The script will automatically handle dependency installation via `uv` when first run.

## 🐛 Troubleshooting

### Common Error Messages

**""❌ Full Disk Access permission required""**
- Follow the [macOS Permissions Setup](#-macos-permissions-setup) section
- Ensure you've restarted your MCP client after granting permission

**""Messages database not found""**
- Make sure you've used the Messages app at least once
- Verify iMessage is enabled in Messages preferences

**""Invalid phone number""**
- Phone numbers are validated using Google's phonenumbers library
- Try using E.164 format (e.g., ""+1234567890"")
- US numbers without country code will be assumed to be US numbers

### Getting Help

If you encounter issues:
1. Check the error message for specific guidance
2. Ensure your MCP client has Full Disk Access permission
3. Verify the Messages app has been used and iMessage is enabled
4. Try testing the server directly with mcptools (see Advanced Usage)
","Star
 57",2025-06-24 07:57:58.098202,imessage-query-fastmcp-mcp-server - MCP Server | Model Context Protocol Integration,[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/hannesrudolph-imessage-query-fastmcp-mcp-server-badge.png)](https://mseep.ai/app/hannesrudolph-im...,"['mcp server', 'model context protocol', 'ai integration', 'imessage-query-fastmcp-mcp-server']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'imessage-query-fastmcp-mcp-server', 'description': '[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/hannesrudolph-imessage-query-fastmcp-mcp-server-badge.png)](https://mseep.ai/app/hannesrudolph-im...', 'url': 'https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server', 'codeRepository': 'https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Python,Database,Search Engine,,get_chat_transcript,"{
  ""mcpServers"": {
    ""imessage-query"": {
      ""command"": ""/full/path/to/imessage-query-server.py""
    }
  }
}",,,,hannesrudolph,"An MCP server that provides safe access to your iMessage database through Model Context Protocol (MCP). This server is built with the FastMCP framework and the imessagedb library, enabling LLMs to que..."
https://github.com/oschina/gitee,oschina/gitee,,,2025-06-24 07:57:58.098202,gitee - MCP Server | Model Context Protocol Integration,,"['mcp server', 'model context protocol', 'ai integration', 'gitee']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'gitee', 'description': '', 'url': 'https://github.com/oschina/gitee', 'codeRepository': 'https://github.com/oschina/gitee', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Unknown,Other,,,,,,,,oschina,
https://github.com/nick1udwig/ws-mcp,nick1udwig/ws-mcp,"# ws-mcp

Wrap MCP stdio servers with a WebSocket.
For use with [kibitz](https://github.com/nick1udwig/kibitz).

## Quickstart

### Prerequisites

Install [uv](https://github.com/astral-sh/uv):
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### Configuration

The config file specifies which MCP servers to run.
The default config (no `--config` or `--command` args provided) includes:
- [`wcgw`](https://github.com/rusiaaman/wcgw): For general system operations and file management
- [`fetch`](https://github.com/modelcontextprotocol/servers/tree/main/src/fetch): For making HTTP requests

To make a configuration file:

1. Create your configuration file:
   ```bash
   cp sample.config.json config.json
   ```
2. Modify `config.json` to add or remove servers based on your needs.
3. Run with `--config path/to/config.json` to use the new config file.

### Running ws-mcp

Basic usage with default config file (no `--config` or `--command` provided) and port:
```bash
uvx --refresh ws-mcp@latest
```

This will start all configured servers on the default port (`10125`).

To use a config file and port:
```bash
uvx --refresh ws-mcp@latest --config path/to/config --port 10125
```

## Detailed Usage

```bash
# Example using fetch
uvx --refresh ws-mcp --command ""uvx mcp-server-fetch"" --port 3002

# Example using wcgw
## On macOS
uvx --refresh ws-mcp --command ""uvx --from wcgw@latest --python 3.12 wcgw_mcp"" --port 3001

## On Linux (or if you have issues on macOS with wcgw)
cd /tmp
git clone https://github.com/nick1udwig/wcgw.git
cd wcgw
git submodule update --init --recursive
git checkout hf/fix-wcgw-on-ubuntu
cd ..
uvx --refresh ws-mcp --command ""uvx --from /tmp/wcgw --with /tmp/wcgw/src/mcp_wcgw --python 3.12 wcgw_mcp"" --port 3001

# Example using Brave search
export BRAVE_API_KEY=YOUR_API_KEY_HERE
uvx --refresh ws-mcp --env BRAVE_API_KEY=$BRAVE_API_KEY --command ""npx -y @modelcontextprotocol/server-brave-search"" --port 3003

# Or, with a .env file:
uvx --refresh ws-mcp --env-file path/to/.env --command ""npx -y @modelcontextprotocol/server-brave-search"" --port 3003

# `--command` can be supplied multiple times!
#  Example serving multiple servers at once:
uvx --refresh ws-mcp --env-file path/to/.env --command ""npx -y @modelcontextprotocol/server-brave-search"" --command ""uvx mcp-server-fetch"" --port 3004

# Servers can also be specified in a `.json` file following [the standard MCP format](https://modelcontextprotocol.io/quickstart/user#2-add-the-filesystem-mcp-server)
uvx --refresh ws-mcp --env-file path/to/.env --config path/to/config.json --port 3005
```
","Star
 19",2025-06-24 07:57:58.098202,ws-mcp - MCP Server | Model Context Protocol Integration,"# ws-mcp

Wrap MCP stdio servers with a WebSocket.
For use with [kibitz](https://github.com/nick1udwig/kibitz).

## Quickstart

### Prerequisites

Install [u...","['mcp server', 'model context protocol', 'ai integration', 'ws-mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'ws-mcp', 'description': '# ws-mcp\n\nWrap MCP stdio servers with a WebSocket.\nFor use with [kibitz](https://github.com/nick1udwig/kibitz).\n\n## Quickstart\n\n### Prerequisites\n\nInstall [u...', 'url': 'https://github.com/nick1udwig/ws-mcp', 'codeRepository': 'https://github.com/nick1udwig/ws-mcp', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",JavaScript,Development,Text Processing,,"Configuration, Prerequisites",,,,,nick1udwig,Wrap MCP stdio servers with a WebSocket.
https://github.com/pab1it0/adx-mcp-server,pab1it0/adx-mcp-server,"# Azure Data Explorer MCP Server

<a href=""https://glama.ai/mcp/servers/1yysyd147h"">
  <img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/1yysyd147h/badge"" />
</a>

A [Model Context Protocol][mcp] (MCP) server for Azure Data Explorer/Eventhouse in Microsoft Fabric.

This provides access to your Azure Data Explorer/Eventhouse clusters and databases through standardized MCP interfaces, allowing AI assistants to execute KQL queries and explore your data.

[mcp]: https://modelcontextprotocol.io

## Features

- [x] Execute KQL queries against Azure Data Explorer
- [x] Discover and explore database resources
  - [x] List tables in the configured database
  - [x] View table schemas
  - [x] Sample data from tables
  - [x] Get table statistics/details

- [x] Authentication support
  - [x] Token credential support (Azure CLI, MSI, etc.)
  - [x] Workload Identity credential support for AKS
- [x] Docker containerization support

- [x] Provide interactive tools for AI assistants

The list of tools is configurable, so you can choose which tools you want to make available to the MCP client.
This is useful if you don't use certain functionality or if you don't want to take up too much of the context window.

## Usage

1. Login to your Azure account which has the permission to the ADX cluster using Azure CLI.

2. Configure the environment variables for your ADX cluster, either through a `.env` file or system environment variables:

```env
# Required: Azure Data Explorer configuration
ADX_CLUSTER_URL=https://yourcluster.region.kusto.windows.net
ADX_DATABASE=your_database

# Optional: Azure Workload Identity credentials 
# AZURE_TENANT_ID=your-tenant-id
# AZURE_CLIENT_ID=your-client-id 
# ADX_TOKEN_FILE_PATH=/var/run/secrets/azure/tokens/azure-identity-token
```

#### Azure Workload Identity Support

The server now uses WorkloadIdentityCredential by default when running in Azure Kubernetes Service (AKS) environments with workload identity configured. It prioritizes the use of WorkloadIdentityCredential whenever the necessary environment variables are present.

For AKS with Azure Workload Identity, you only need to:
1. Make sure the pod has `AZURE_TENANT_ID` and `AZURE_CLIENT_ID` environment variables set
2. Ensure the token file is mounted at the default path or specify a custom path with `ADX_TOKEN_FILE_PATH`

If these environment variables are not present, the server will automatically fall back to DefaultAzureCredential, which tries multiple authentication methods in sequence.

3. Add the server configuration to your client configuration file. For example, for Claude Desktop:

```json
{
  ""mcpServers"": {
    ""adx"": {
      ""command"": ""uv"",
      ""args"": [
        ""--directory"",
        ""<full path to adx-mcp-server directory>"",
        ""run"",
        ""src/adx_mcp_server/main.py""
      ],
      ""env"": {
        ""ADX_CLUSTER_URL"": ""https://yourcluster.region.kusto.windows.net"",
        ""ADX_DATABASE"": ""your_database""
      }
    }
  }
}
```

> Note: if you see `Error: spawn uv ENOENT` in Claude Desktop, you may need to specify the full path to `uv` or set the environment variable `NO_UV=1` in the configuration.

## Docker Usage

This project includes Docker support for easy deployment and isolation.

### Building the Docker Image

Build the Docker image using:

```bash
docker build -t adx-mcp-server .
```

### Running with Docker

You can run the server using Docker in several ways:

#### Using docker run directly:

```bash
docker run -it --rm \
  -e ADX_CLUSTER_URL=https://yourcluster.region.kusto.windows.net \
  -e ADX_DATABASE=your_database \
  -e AZURE_TENANT_ID=your_tenant_id \
  -e AZURE_CLIENT_ID=your_client_id \
  adx-mcp-server
```

#### Using docker-compose:

Create a `.env` file with your Azure Data Explorer credentials and then run:

```bash
docker-compose up
```

### Running with Docker in Claude Desktop

To use the containerized server with Claude Desktop, update the configuration to use Docker with the environment variables:

```json
{
  ""mcpServers"": {
    ""adx"": {
      ""command"": ""docker"",
      ""args"": [
        ""run"",
        ""--rm"",
        ""-i"",
        ""-e"", ""ADX_CLUSTER_URL"",
        ""-e"", ""ADX_DATABASE"",
        ""-e"", ""AZURE_TENANT_ID"",
        ""-e"", ""AZURE_CLIENT_ID"",
        ""-e"", ""ADX_TOKEN_FILE_PATH"",
        ""adx-mcp-server""
      ],
      ""env"": {
        ""ADX_CLUSTER_URL"": ""https://yourcluster.region.kusto.windows.net"",
        ""ADX_DATABASE"": ""your_database"",
        ""AZURE_TENANT_ID"": ""your_tenant_id"",
        ""AZURE_CLIENT_ID"": ""your_client_id"",
        ""ADX_TOKEN_FILE_PATH"": ""/var/run/secrets/azure/tokens/azure-identity-token""
      }
    }
  }
}
```

This configuration passes the environment variables from Claude Desktop to the Docker container by using the `-e` flag with just the variable name, and providing the actual values in the `env` object.

## Using as a Dev Container / GitHub Codespace

This repository can also be used as a development container for a seamless development experience. The dev container setup is located in the `devcontainer-feature/adx-mcp-server` folder.

For more details, check the [devcontainer README](devcontainer-feature/adx-mcp-server/README.md).



## Development

Contributions are welcome! Please open an issue or submit a pull request if you have any suggestions or improvements.

This project uses [`uv`](https://github.com/astral-sh/uv) to manage dependencies. Install `uv` following the instructions for your platform:

```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

You can then create a virtual environment and install the dependencies with:

```bash
uv venv
source .venv/bin/activate  # On Unix/macOS
.venv\Scripts\activate     # On Windows
uv pip install -e .
```

## Project Structure

The project has been organized with a `src` directory structure:

```
adx-mcp-server/
├── src/
│   └── adx_mcp_server/
│       ├── __init__.py      # Package initialization
│       ├── server.py        # MCP server implementation
│       ├── main.py          # Main application logic
├── Dockerfile               # Docker configuration
├── docker-compose.yml       # Docker Compose configuration
├── .dockerignore            # Docker ignore file
├── pyproject.toml           # Project configuration
└── README.md                # This file
```

### Testing

The project includes a comprehensive test suite that ensures functionality and helps prevent regressions.

Run the tests with pytest:

```bash
# Install development dependencies
uv pip install -e "".[dev]""

# Run the tests
pytest

# Run with coverage report
pytest --cov=src --cov-report=term-missing
```
Tests are organized into:

- Configuration validation tests
- Server functionality tests
- Error handling tests
- Main application tests

When adding new features, please also add corresponding tests.

### Tools

| Tool | Category | Description |
| --- | --- | --- |
| `execute_query` | Query | Execute a KQL query against Azure Data Explorer |
| `list_tables` | Discovery | List all tables in the configured database |
| `get_table_schema` | Discovery | Get the schema for a specific table |
| `sample_table_data` | Discovery | Get sample data from a table with optional sample size |


## License

MIT

---

[mcp]: https://modelcontextprotocol.io
","Star
 42",2025-06-24 07:57:58.098202,adx-mcp-server - MCP Server | Model Context Protocol Integration,"# Azure Data Explorer MCP Server

<a href=""https://glama.ai/mcp/servers/1yysyd147h"">
  <img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/1yysyd...","['mcp server', 'model context protocol', 'ai integration', 'adx-mcp-server']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'adx-mcp-server', 'description': '# Azure Data Explorer MCP Server\n\n<a href=""https://glama.ai/mcp/servers/1yysyd147h"">\n  <img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/1yysyd...', 'url': 'https://github.com/pab1it0/adx-mcp-server', 'codeRepository': 'https://github.com/pab1it0/adx-mcp-server', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Python,AI Tools,Cloud Services,"pip install -e . | pip install -e "".[dev]""","Testing, Tools","{
  ""mcpServers"": {
    ""adx"": {
      ""command"": ""uv"",
      ""args"": [
        ""--directory"",
        ""<full path to adx-mcp-server directory>"",
        ""run"",
        ""src/adx_mcp_server/main.py""
      ],
      ""env"": {
        ""ADX_CLUSTER_URL"": ""https://yourcluster.region.kusto.windows.net"",
        ""ADX_DATABASE"": ""your_database""
      }
    }
  }
}","-e, .[dev, NO_UV",MIT,,pab1it0,"<a href=""https://glama.ai/mcp/servers/1yysyd147h"">"
https://github.com/integromat/make-mcp-server,integromat/make-mcp-server,"# Make MCP Server (legacy)

**A modern, cloud-based version of the Make MCP Server is now available. For most use cases, we recommend using [this new version](https://developers.make.com/mcp-server).**

A Model Context Protocol server that enables Make scenarios to be utilized as tools by AI assistants. This integration allows AI systems to trigger and interact with your Make automation workflows.

## How It Works

The MCP server:

-   Connects to your Make account and identifies all scenarios configured with ""On-Demand"" scheduling
-   Parses and resolves input parameters for each scenario, providing AI assistants with meaningful parameter descriptions
-   Allows AI assistants to invoke scenarios with appropriate parameters
-   Returns scenario output as structured JSON, enabling AI assistants to properly interpret the results

## Benefits

-   Turn your Make scenarios into callable tools for AI assistants
-   Maintain complex automation logic in Make while exposing functionality to AI systems
-   Create bidirectional communication between your AI assistants and your existing automation workflows

## Usage with Claude Desktop

### Prerequisites

-   NodeJS
-   MCP Client (like Claude Desktop App)
-   Make API Key with `scenarios:read` and `scenarios:run` scopes

### Installation

To use this server with the Claude Desktop app, add the following configuration to the ""mcpServers"" section of your `claude_desktop_config.json`:

```json
{
    ""mcpServers"": {
        ""make"": {
            ""command"": ""npx"",
            ""args"": [""-y"", ""@makehq/mcp-server""],
            ""env"": {
                ""MAKE_API_KEY"": ""<your-api-key>"",
                ""MAKE_ZONE"": ""<your-zone>"",
                ""MAKE_TEAM"": ""<your-team-id>""
            }
        }
    }
}
```

-   `MAKE_API_KEY` - You can generate an API key in your Make profile.
-   `MAKE_ZONE` - The zone your organization is hosted in (e.g., `eu2.make.com`).
-   `MAKE_TEAM` - You can find the ID in the URL of the Team page.
","Star
 107",2025-06-24 07:57:58.098202,make-mcp-server - MCP Server | Model Context Protocol Integration,"# Make MCP Server (legacy)

**A modern, cloud-based version of the Make MCP Server is now available. For most use cases, we recommend using [this new version...","['mcp server', 'model context protocol', 'ai integration', 'make-mcp-server']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'make-mcp-server', 'description': '# Make MCP Server (legacy)\n\n**A modern, cloud-based version of the Make MCP Server is now available. For most use cases, we recommend using [this new version...', 'url': 'https://github.com/integromat/make-mcp-server', 'codeRepository': 'https://github.com/integromat/make-mcp-server', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Rust,AI Tools,Claude Desktop,,"Installation, Prerequisites","{
    ""mcpServers"": {
        ""make"": {
            ""command"": ""npx"",
            ""args"": [""-y"", ""@makehq/mcp-server""],
            ""env"": {
                ""MAKE_API_KEY"": ""<your-api-key>"",
                ""MAKE_ZONE"": ""<your-zone>"",
                ""MAKE_TEAM"": ""<your-team-id>""
            }
        }
    }
}",,,,integromat,"A modern, cloud-based version of the Make MCP Server is now available. For most use cases, we recommend using this new version."
https://github.com/wong2/mcp-cli,wong2/mcp-cli,"# mcp-cli

A CLI inspector for the Model Context Protocol

https://github.com/user-attachments/assets/4cd113e9-f097-4c9d-b391-045c5f213183

## Features

- Run MCP servers from various sources
- List Tools, Resources, Prompts
- Call Tools, Read Resources, Read Prompts
- OAuth support for SSE and Streamable HTTP servers

## Usage

### Run without arguments

```bash
npx @wong2/mcp-cli
```

This will use the config file of Claude Desktop.

### Run with a config file

```bash
npx @wong2/mcp-cli -c config.json
```

The config file has the same format as the Claude Desktop config file.

### Run servers from NPM

```bash
npx @wong2/mcp-cli npx <package-name> <args>
```

### Run locally developed server

```bash
npx @wong2/mcp-cli node path/to/server/index.js args...
```

### Connect to a running server over Streamable HTTP

```bash
npx @wong2/mcp-cli --url http://localhost:8000/mcp
```

### Connect to a running server over SSE

```bash
npx @wong2/mcp-cli --sse http://localhost:8000/sse
```

### Purge stored data (OAuth tokens, etc.)

```bash
npx @wong2/mcp-cli purge
```

## Related

- [mcpservers.org](https://mcpservers.org) - A curated list of MCP servers
","Star
 344",2025-06-24 07:57:58.098202,mcp-cli - MCP Server | Model Context Protocol Integration,"# mcp-cli

A CLI inspector for the Model Context Protocol

https://github.com/user-attachments/assets/4cd113e9-f097-4c9d-b391-045c5f213183

## Features

- Ru...","['mcp server', 'model context protocol', 'ai integration', 'mcp-cli']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mcp-cli', 'description': '# mcp-cli\n\nA CLI inspector for the Model Context Protocol\n\nhttps://github.com/user-attachments/assets/4cd113e9-f097-4c9d-b391-045c5f213183\n\n## Features\n\n- Ru...', 'url': 'https://github.com/wong2/mcp-cli', 'codeRepository': 'https://github.com/wong2/mcp-cli', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",JavaScript,File Management,Claude Desktop,,,,,,,wong2,A CLI inspector for the Model Context Protocol
https://github.com/r-huijts/oorlogsbronnen-mcp,r-huijts/oorlogsbronnen-mcp,"# Oorlogsbronnen MCP Server

A Model Context Protocol (MCP) server that provides AI-powered access to the Oorlogsbronnen (War Sources) database. This server enables natural language interactions with historical World War II archives from the Netherlands.

## Natural Language Interaction Examples

Ask your AI assistant questions like these to explore Dutch WWII history:

- **""What happened during the bombing of Rotterdam in May 1940?""**
- **""Tell me about Anne Frank's life in hiding based on historical records.""**
- **""Show me photographs of the Dutch Hunger Winter of 1944-1945.""**
- **""Were any of my ancestors imprisoned in Camp Vught during the war?""**
- **""I'm visiting Arnhem next week. What historical sites related to Operation Market Garden should I see?""**
- **""Find information about resistance activities in Utrecht during the Nazi occupation.""**
- **""What was daily life like for Jewish families in Amsterdam before deportations began?""**
- **""Show me firsthand accounts from people who witnessed the liberation of the Netherlands in 1945.""**
- **""What records exist about children who were hidden by Dutch families during the war?""**
- **""I'm researching the impact of WWII on Dutch infrastructure. Can you find documents about the reconstruction of bridges and railways?""**

## Features

- 🔍 Natural language search across the Oorlogsbronnen database
- 🏷️ Filter results by content type (person, photo, article, etc.)
- 📊 Control the number of results returned
- 🤖 AI-friendly JSON responses for further processing

## Installation

You can install this server in two ways:

### 1. Using Claude Desktop with NPX Package

Update your Claude configuration file (`~/Library/Application Support/Claude/claude_desktop_config.json`):

```json
{
  ""mcpServers"": {
    ""oorlogsbronnen-server"": {
      ""command"": ""npx"",
      ""args"": [
        ""-y"",
        ""oorlogsbronnen-mcp""
      ]
    }
  }
}
```

After updating the configuration, restart Claude Desktop for the changes to take effect.

### 2. From Source

1. Clone this repository:
```bash
git clone https://github.com/r-huijts/oorlogsbronnen-mcp.git
cd oorlogsbronnen-mcp
```

2. Install dependencies:
```bash
npm install
```

3. Build the project:
```bash
npm run build
```

4. Configure Claude Desktop by updating your configuration file (located at `~/Library/Application Support/Claude/claude_desktop_config.json`):

```json
{
  ""mcpServers"": {
    ""oorlogsbronnen-server"": {
      ""command"": ""node"",
      ""args"": [
        ""/absolute/path/to/oorlogsbronnen-mcp/dist/mcp-server.js""
      ]
    }
  }
}
```

Replace `/absolute/path/to/oorlogsbronnen-mcp` with the actual path to your installation.

## Usage Examples

The MCP server understands natural language queries and can help you explore World War II archives. Here are some example queries you can use with Claude:

### Basic Searches

- ""Use search_ww2_nl_archives to find documents about the resistance movement in Amsterdam""
- ""Search the Dutch WW2 archives for information about Jewish refugees in 1942""
- ""Look through the Netherlands war archives for records of Allied bombing raids""

### Filtering by Type

- ""Use search_ww2_nl_archives to show me photographs of the liberation of Rotterdam""
- ""Find personal accounts in the Dutch WW2 archives about life in concentration camps""
- ""Search the Netherlands war archives for newspaper articles about food shortages""

### Specific Queries

- ""Search the Dutch WW2 archives for documents about Anne Frank's time in Amsterdam""
- ""Use search_ww2_nl_archives to find records of the February Strike of 1941""
- ""Look through the Netherlands war archives for information about Operation Market Garden""

### Research Examples

1. **Personal History Research**:
   ```
   Use search_ww2_nl_archives to find any records or documents about the Rosenberg family in Amsterdam between 1940-1945
   ```

2. **Local History**:
   ```
   Search the Dutch WW2 archives for photographs and documents about daily life in Utrecht during the occupation
   ```

3. **Military Operations**:
   ```
   Use search_ww2_nl_archives to find firsthand accounts and official reports about the Battle of the Scheldt
   ```

### Advanced Usage

You can combine different search criteria:
```
Search the Netherlands WW2 archives for photographs and personal accounts of the Dutch famine in 1944-1945, limit to 20 results
```

## API Reference

The server exposes the following MCP tool:

### search_ww2_nl_archives

A powerful search tool designed to query the Oorlogsbronnen (War Sources) database for World War II related content in the Netherlands. This tool can be used to find historical documents, photographs, personal accounts, and other archival materials from 1940-1945.

**When to use this tool:**
- Searching for specific historical events during WWII in the Netherlands
- Finding information about people, places, or organizations during the war
- Locating photographs or documents from specific time periods or locations
- Researching personal or family history related to WWII
- Finding primary sources about the Dutch resistance, occupation, or liberation
- Discovering materials about Jewish life and persecution during the war
- Researching military operations that took place in the Netherlands

Parameters:
- `query` (required): 
  - Type: string
  - Description: The main search term or phrase to look for in the archives
  - Can include: names, places, dates, events, or descriptive terms
  - Examples:
    - ""Anne Frank""
    - ""Rotterdam bombing 1940""
    - ""Dutch resistance Amsterdam""
    - ""Jewish deportation Westerbork""
    - ""Operation Market Garden""

- `type` (optional):
  - Type: string
  - Description: Filter results by specific content type
  - Available types:
    - ""person"": Individual biographical records
    - ""photo"": Historical photographs
    - ""article"": News articles and written documents
    - ""video"": Video footage
    - ""object"": Physical artifacts and objects
    - ""location"": Places and geographical records
    - ""book"": Published books, memoirs, and monographs
  - Use when: You need to focus on specific types of historical materials
  - Default: All types included

- `count` (optional):
  - Type: number
  - Description: Number of results to return in the response
  - Minimum: 1
  - Maximum: 100
  - Default: 10
  - Use when: You need to control the volume of returned results
  - Note: Larger numbers will provide more comprehensive results but may take longer to process

**Response Format:**
```json
{
  ""results"": [
    {
      ""id"": string,          // Unique identifier for the record
      ""title"": string,       // Title or name of the item
      ""type"": string,        // Content type (person, photo, article, etc.)
      ""description"": string, // Detailed description (if available)
      ""url"": string         // Direct link to view the item on Oorlogsbronnen
    }
  ]
}
```

**Example Queries and Their Tool Calls:**

1. Basic Historical Search:
```typescript
{
  query: ""February Strike 1941"",
  type: ""article"",
  count: 5
}
```

2. Person Research:
```typescript
{
  query: ""Rosenberg family Amsterdam Jewish"",
  type: ""person"",
  count: 20
}
```

3. Photo Collection Search:
```typescript
{
  query: ""liberation celebrations Amsterdam Dam Square 1945"",
  type: ""photo"",
  count: 15
}
```

**Error Handling:**
- The tool will return an error message if:
  - The query is empty or contains invalid characters
  - The specified type is not supported
  - The count is outside the valid range (1-100)
  - The API is temporarily unavailable
  - Rate limits are exceeded

**Best Practices:**
1. Start with broader searches and narrow down with specific terms
2. Use location names to focus on specific areas
3. Include dates when searching for specific events
4. Combine person names with locations for family research
5. Use type filtering to focus on specific kinds of historical materials

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Oorlogsbronnen for providing access to their valuable historical archives
- The Model Context Protocol (MCP) community for enabling AI-powered interactions ","Star
 7",2025-06-24 07:57:58.098202,oorlogsbronnen-mcp - MCP Server | Model Context Protocol Integration,"# Oorlogsbronnen MCP Server

A Model Context Protocol (MCP) server that provides AI-powered access to the Oorlogsbronnen (War Sources) database. This server ...","['mcp server', 'model context protocol', 'ai integration', 'oorlogsbronnen-mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'oorlogsbronnen-mcp', 'description': '# Oorlogsbronnen MCP Server\n\nA Model Context Protocol (MCP) server that provides AI-powered access to the Oorlogsbronnen (War Sources) database. This server ...', 'url': 'https://github.com/r-huijts/oorlogsbronnen-mcp', 'codeRepository': 'https://github.com/r-huijts/oorlogsbronnen-mcp', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Rust,AI Tools,Search Engine,npm install,search_ww2_nl_archives,"{
  ""mcpServers"": {
    ""oorlogsbronnen-server"": {
      ""command"": ""npx"",
      ""args"": [
        ""-y"",
        ""oorlogsbronnen-mcp""
      ]
    }
  }
}",,MIT,,r-huijts,A Model Context Protocol (MCP) server that provides AI-powered access to the Oorlogsbronnen (War Sources) database. This server enables natural language interactions with historical World War II archi...
https://github.com/r-huijts/firstcycling-mcp,r-huijts/firstcycling-mcp,"# FirstCycling MCP Server

This is a Model Context Protocol (MCP) server that provides professional cycling data from FirstCycling. It allows you to retrieve comprehensive information about professional cyclists, race results, race details, and historical cycling data.

## Features

This MCP server offers rich access to professional cycling data, providing tools for:

- Finding information about professional cyclists
- Retrieving race results and details
- Exploring historical race data
- Analyzing rider performance and career progression
- Accessing information about cycling teams and competitions

## Real-World Use Cases

With this MCP server, you can use Claude to:

### Rider Analysis

- **Performance Tracking**: ""How has Tadej Pogačar performed in the Tour de France over the years?""
- **Career Progression**: ""Show me the team history and career progression of Wout van Aert.""
- **Specialization Analysis**: ""What are Mathieu van der Poel's results in Monument classics?""
- **Victory Analysis**: ""List all WorldTour victories for Jonas Vingegaard.""
- **Historical Comparison**: ""Compare the Grand Tour results of Primož Roglič and Jonas Vingegaard.""

### Race Research

- **Recent Results**: ""Show me the results of the 2023 Paris-Roubaix.""
- **Historical Context**: ""Who are the youngest and oldest winners of the Tour of Flanders?""
- **Team Analysis**: ""Get the startlist for the 2023 Tour de France with detailed team information.""
- **Race Statistics**: ""Show me the victory table for Liège-Bastogne-Liège. Who has won it the most times?""
- **Stage Information**: ""Can you show me the stage profiles for the 2023 Giro d'Italia?""

### Sports Journalism

- ""Create a detailed profile of Remco Evenepoel for a cycling magazine article.""
- ""Write a preview for the upcoming Tour de France based on the recent results of top contenders like Tadej Pogačar and Jonas Vingegaard.""
- ""Analyze the evolution of Tom Pidcock's career based on his race results and team history.""

### Cycling Education

- ""Explain what makes the Monument classics special using data about their history and winners.""
- ""Create an educational summary about Grand Tours and their significance in professional cycling.""
- ""Describe the typical career progression of a professional cyclist using examples from the data.""

## Requirements

- Python 3.10 or higher
- `uv` package manager (recommended)
- Dependencies as listed in `pyproject.toml`, including:
  - mcp
  - beautifulsoup4
  - lxml
  - pandas
  - slumber
  - and other packages for web scraping and data processing

## Setup

1. Clone this repository
2. Create and activate a virtual environment:
   ```
   uv venv
   source .venv/bin/activate  # On macOS/Linux
   # or
   .venv\Scripts\activate  # On Windows
   ```
3. Install dependencies:
   ```
   uv pip install -e .
   ```

## FirstCycling API

This server uses the [FirstCycling API](https://github.com/baronet2/FirstCyclingAPI), which has been integrated directly into the project. The API provides methods to fetch data from the FirstCycling website through web scraping.

## MCP Tools

The server exposes the following tools through the Model Context Protocol:

### Rider Information

| Tool | Description |
|------|-------------|
| `get_rider_info` | Get basic biographical information about a rider including nationality, birthdate, weight, height, and current team |
| `get_rider_best_results` | Retrieve a rider's best career results, sorted by importance |
| `get_rider_grand_tour_results` | Get a rider's results in Grand Tours (Tour de France, Giro d'Italia, Vuelta a España) |
| `get_rider_monument_results` | Retrieve a rider's results in cycling's Monument classics |
| `get_rider_team_and_ranking` | Get a rider's team history and UCI ranking evolution over time |
| `get_rider_race_history` | Retrieve a rider's complete race participation history, optionally filtered by year |
| `get_rider_one_day_races` | Get a rider's results in one-day races, optionally filtered by year |
| `get_rider_stage_races` | Get a rider's results in multi-day stage races, optionally filtered by year |
| `get_rider_teams` | Retrieve the complete team history of a rider throughout their career |
| `get_rider_victories` | Get a list of a rider's career victories, with optional filters for WorldTour or UCI races |

### Race Information

| Tool | Description |
|------|-------------|
| `get_race_results` | Retrieve results for a specific race edition by race ID and year |
| `get_race_overview` | Get general information about a race including history, records, and past winners |
| `get_race_stage_profiles` | Retrieve stage profiles and details for multi-stage races |
| `get_race_startlist` | Get the startlist for a specific race edition with detailed or basic team information |
| `get_race_victory_table` | Retrieve the all-time victory table for a race showing riders with most wins |
| `get_race_year_by_year` | Get year-by-year results for a race with optional classification filter |
| `get_race_youngest_oldest_winners` | Retrieve information about the youngest and oldest winners of a race |
| `get_race_stage_victories` | Get information about stage victories in multi-stage races |

### Search Tools

| Tool | Description |
|------|-------------|
| `search_rider` | Search for riders by name, returning their IDs and basic information |
| `search_race` | Search for races by name, returning their IDs and basic information |

## Usage

### Development Mode

You can test the server with MCP Inspector by running:

```
uv run mcp dev firstcycling.py
```

This will start the server and open the MCP Inspector in your browser, allowing you to test the available tools.

### Integration with Claude for Desktop

To integrate this server with Claude for Desktop:

1. Edit the Claude for Desktop config file, located at:
   - macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - Windows: `%APPDATA%\Claude\claude_desktop_config.json`

2. Add the server to your configuration:
   ```json
   {
     ""mcpServers"": {
       ""firstcycling"": {
         ""command"": ""uv"",
         ""args"": [""--directory"", ""/path/to/server/directory"", ""run"", ""firstcycling.py""]
       }
     }
   }
   ```

3. Restart Claude for Desktop

## License

MIT
","Star
 9",2025-06-24 07:57:58.098202,firstcycling-mcp - MCP Server | Model Context Protocol Integration,"# FirstCycling MCP Server

This is a Model Context Protocol (MCP) server that provides professional cycling data from FirstCycling. It allows you to retrieve...","['mcp server', 'model context protocol', 'ai integration', 'firstcycling-mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'firstcycling-mcp', 'description': '# FirstCycling MCP Server\n\nThis is a Model Context Protocol (MCP) server that provides professional cycling data from FirstCycling. It allows you to retrieve...', 'url': 'https://github.com/r-huijts/firstcycling-mcp', 'codeRepository': 'https://github.com/r-huijts/firstcycling-mcp', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Python,Development,Search Engine,pip install -e .,,"""firstcycling"": {
         ""command"": ""uv"",
         ""args"": [""--directory"", ""/path/to/server/directory"", ""run"", ""firstcycling.py""]",-e,MIT,,r-huijts,"This is a Model Context Protocol (MCP) server that provides professional cycling data from FirstCycling. It allows you to retrieve comprehensive information about professional cyclists, race results, ..."
