# {{folder_name}}

{{folder_name}} is a CrewAI Tool. This template is designed to help you create
custom tools to power up your crews.

## Installing

Ensure you have Python >=3.10 <3.14 installed on your system. This project
uses [UV](https://docs.astral.sh/uv/) for dependency management and package
handling, offering a seamless setup and execution experience.

First, if you haven't already, install `uv`:

```bash
pip install uv
```

Next, navigate to your project directory and install the dependencies with:

```bash
crewai install
```

## Publishing

Collaborate by sharing tools within your organization, or publish them publicly
to contribute with the community.

```bash
crewai tool publish {{tool_name}}
```

Others may install your tool in their crews running:

```bash
crewai tool install {{tool_name}}
```

## Support

For support, questions, or feedback regarding the {{crew_name}} tool or CrewAI.

- Visit our [documentation](https://docs.crewai.com)
- Reach out to us through our [GitHub repository](https://github.com/joaomdmoura/crewai)
- [Join our Discord](https://discord.com/invite/X4JWnZnxPb)
- [Chat with our docs](https://chatg.pt/DWjSBZn)

Let's create wonders together with the power and simplicity of crewAI.
