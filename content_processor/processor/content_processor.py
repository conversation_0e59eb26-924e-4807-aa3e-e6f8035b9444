"""Enhanced Event-Driven Content Processor for MCP server analysis."""
import json
import os
from typing import List, Dict, Any
from datetime import datetime
import slugify
from crewai import Crew, Process, LLM
import csv
import time
import logging

# Event-driven imports
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logging.warning("Redis not available. Running in standalone mode.")

from ..agents.content_agents import create_content_agents
from ..tasks.content_tasks import create_content_tasks

class EventBus:
    """Simple Redis-based event bus for component communication"""

    def __init__(self, redis_url="redis://localhost:6379"):
        self.redis_client = None
        if REDIS_AVAILABLE:
            try:
                self.redis_client = redis.from_url(redis_url, decode_responses=True)
                self.redis_client.ping()
                logging.info("Content processor connected to Redis event bus")
            except Exception as e:
                logging.warning(f"Redis not available: {e}. Running in standalone mode.")

    def publish_event(self, event_type: str, data: dict):
        """Publish event to the bus"""
        if not self.redis_client:
            return

        event = {
            "event_type": event_type,
            "data": data,
            "timestamp": datetime.now().isoformat(),
            "source": "content_processor"
        }

        try:
            self.redis_client.lpush(f"events:{event_type}", json.dumps(event))
            self.redis_client.publish(f"channel:{event_type}", json.dumps(event))
            logging.info(f"Published event: {event_type}")
        except Exception as e:
            logging.error(f"Failed to publish event: {e}")

    def get_events(self, event_type: str, timeout: int = 30):
        """Get events from queue"""
        if not self.redis_client:
            return None

        try:
            result = self.redis_client.brpop(f"events:{event_type}", timeout=timeout)
            if result:
                return json.loads(result[1])
        except Exception as e:
            logging.error(f"Error getting events: {e}")
        return None

class SEOEnhancer:
    """Enhanced SEO optimization for generated content"""

    def __init__(self):
        self.target_keywords = ["mcp server", "model context protocol", "ai integration", "claude mcp"]

    def enhance_content(self, content: dict) -> dict:
        """Apply advanced SEO enhancements"""
        try:
            # Enhance title
            if "title" in content:
                content["seo_title"] = f"{content['title']} - Complete MCP Server Guide | Model Context Protocol"

            # Enhance description
            if "overview" in content:
                overview = content["overview"]
                # Ensure keywords are present
                for keyword in self.target_keywords[:3]:
                    if keyword.lower() not in overview.lower():
                        overview = f"{keyword} integration: {overview}"
                content["seo_enhanced_overview"] = overview

            # Add structured data
            content["seo_structured_data"] = {
                "@context": "https://schema.org",
                "@type": "SoftwareApplication",
                "name": content.get("title", ""),
                "description": content.get("overview", "")[:200],
                "applicationCategory": "DeveloperApplication",
                "operatingSystem": "Cross-platform",
                "keywords": self.target_keywords
            }

            # Add Open Graph metadata
            content["seo_open_graph"] = {
                "og:title": content.get("seo_title", ""),
                "og:description": content.get("overview", "")[:200],
                "og:type": "website",
                "og:site_name": "MCP Server Directory"
            }

            logging.info(f"Enhanced SEO for content: {content.get('title', 'Unknown')}")

        except Exception as e:
            logging.error(f"Error in SEO enhancement: {e}")

        return content

# Global instances
event_bus = EventBus()
seo_enhancer = SEOEnhancer()

run_instructions = {
  "overview": "There are two ways to add an MCP server to Cursor and Claude Desktop App:\n\n1. **Globally**: Available in all of your projects by adding it to the global MCP settings file.\n2. **Per Project**: Available only within a specific project by adding it to the project's MCP settings file.",
  "cursor": {
    "global_instructions": {
      "title": "Adding an MCP Server to Cursor Globally",
      "steps": [
        "Go to **Cursor Settings > MCP** and click **Add new global MCP server**.",
        "This will open the `~/.cursor/mcp.json` file.",
        "Add your MCP server configuration like the following:"
      ],
      "example": {
        "path": "~/.cursor/mcp.json",
        "content": {
          "mcpServers": {
            "cursor-rules-mcp": {
              "command": "npx",
              "args": [
                "-y",
                "cursor-rules-mcp"
              ]
            }
          }
        }
      }
    },
    "project_instructions": {
      "title": "Adding an MCP Server to a Project",
      "steps": [
        "In your project folder, create or edit the `.cursor/mcp.json` file.",
        "Add your MCP server configuration (same format as the global example):"
      ],
      "example": {
        "path": ".cursor/mcp.json",
        "content": {
          "mcpServers": {
            "cursor-rules-mcp": {
              "command": "npx",
              "args": [
                "-y",
                "cursor-rules-mcp"
              ]
            }
          }
        }
      }
    }
  },
  "claude": {
    "global_instructions": {
      "title": "Adding an MCP Server to Claude Desktop App Globally",
      "steps": [
        "Go to **Claude Settings > MCP Servers** and click **Add Global MCP Server**.",
        "This will open the `~/.claude/mcp.json` file (or you can navigate there manually).",
        "Add your MCP server configuration like the following:"
      ],
      "example": {
        "path": "~/.claude/mcp.json",
        "content": {
          "mcpServers": {
            "cursor-rules-mcp": {
              "command": "npx",
              "args": [
                "-y",
                "cursor-rules-mcp"
              ]
            }
          }
        }
      }
    },
    "project_instructions": {
      "title": "Adding an MCP Server to a Project in Claude",
      "steps": [
        "In your project's root folder, create or edit the `.claude/mcp.json` file.",
        "Add your MCP server configuration in the same format as the global example:"
      ],
      "example": {
        "path": ".claude/mcp.json",
        "content": {
          "mcpServers": {
            "cursor-rules-mcp": {
              "command": "npx",
              "args": [
                "-y",
                "cursor-rules-mcp"
              ]
            }
          }
        }
      }
    }
  },
  "usage": {
    "title": "How to Use the MCP Server",
    "instructions": [
      "After installation, return to **Settings > MCP** (in Cursor or Claude) and click the **refresh** button.",
      "The agent will detect and list the tools provided by the MCP server.",
      "You can also explicitly instruct the agent to use a tool by mentioning its name and describing what you want it to do."
    ]
  }
}

class ContentProcessor:
    """Enhanced Event-Driven Content Processor using CrewAI with SEO optimization."""

    def __init__(self, input_file: str = "data/backup.json",
                 output_file: str = "data/output.json",
                 batch_size: int = 2,
                 event_driven: bool = True):
        # Ensure data directory exists
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        self.input_file = input_file
        self.output_file = output_file
        self.batch_size = batch_size
        self.processed_urls = self._load_processed_urls()
        self.event_driven = event_driven

        # Configure CrewAI LLM with Azure OpenAI
        self.llm = LLM(
            model=f"azure/{os.getenv('AZURE_OPENAI_DEPLOYMENT_NAME', 'article-generator-trail')}",
            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
            base_url=os.getenv("AZURE_OPENAI_BASEURL"),
            api_version=os.getenv("AZURE_OPENAI_API_VERSION", "2024-08-01-preview")
        )

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def _load_processed_urls(self) -> set:
        """Load already processed URLs and IDs from output file."""
        if not os.path.exists(self.output_file):
            return set()
        try:
            with open(self.output_file, 'r') as f:
                data = json.load(f)
                processed = set()
                for item in data:
                    content = item.get('content', {})
                    # Add multiple URL formats and ID to prevent duplicates
                    if content.get('github_url'):
                        processed.add(content['github_url'])
                    if content.get('github_repo'):
                        processed.add(f"https://github.com/{content['github_repo']}")
                        processed.add(content['github_repo'])  # Also add slug format
                    if content.get('id'):
                        processed.add(content['id'])
                    # Also check old format for backward compatibility
                    if item.get('github', {}).get('url'):
                        processed.add(item['github']['url'])
                return processed
        except Exception as e:
            print(f"Error loading processed URLs: {e}")
            return set()

    def _load_data(self) -> List[Dict[str, Any]]:
        """Load data from input file (CSV or JSON)."""
        if self.input_file.endswith('.csv'):
            return self._load_data_from_csv()
        else:
            try:
                with open(self.input_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error loading input data: {e}")
                return []

    def _load_data_from_csv(self) -> List[Dict[str, Any]]:
        """Load and map data from CSV for CrewAI agent creation."""
        data = []
        try:
            with open(self.input_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    # Only take the required fields and map to expected keys
                    # Clean up stars field - use the robust parsing method
                    stars_raw = row.get("stars", "0")

                    item = {
                        "title": row.get("slug", "").split("/")[-1] if row.get("slug") else row.get("name", ""),
                        "gh_url": row.get("repo_url", ""),
                        "slug": row.get("slug", ""),
                        "gh_stars": self._parse_star_count(stars_raw),
                        "readme": row.get("readme", ""),
                        "topics": row.get("topics", ""),
                        # Add enhanced fields from CSV
                        "language": row.get("language", ""),
                        "category": row.get("category", ""),
                        "subcategory": row.get("subcategory", ""),
                        "installation_command": row.get("installation_command", ""),
                        "tools_list": row.get("tools_list", ""),
                        "config_example": row.get("config_example", ""),
                        "dependencies": row.get("dependencies", ""),
                        "license": row.get("license", ""),
                        "author": row.get("author", ""),
                        "description_short": row.get("description_short", ""),
                    }
                    data.append(item)
        except Exception as e:
            print(f"Error loading CSV input data: {e}")
        return data

    def _save_data(self, data: List[Dict[str, Any]]):
        """Save processed data to output file with deduplication."""
        try:
            existing_data = []
            if os.path.exists(self.output_file):
                with open(self.output_file, 'r') as f:
                    try:
                        existing_data = json.load(f)
                    except json.JSONDecodeError:
                        print("Warning: Output file was corrupted, starting fresh")
                        existing_data = []

            # Deduplicate before saving
            combined_data = existing_data + data
            unique_data = self._deduplicate_content(combined_data)

            print(f"💾 Saving {len(unique_data)} unique items (was {len(combined_data)} before deduplication)")

            # Ensure output directory exists
            os.makedirs(os.path.dirname(self.output_file), exist_ok=True)

            with open(self.output_file, 'w') as f:
                json.dump(unique_data, f, indent=2)
        except Exception as e:
            print(f"Error saving data: {e}")

    def _deduplicate_content(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate content based on multiple identifiers."""
        seen_identifiers = set()
        unique_data = []

        for item in data:
            content = item.get('content', {})

            # Create multiple identifiers for this item
            identifiers = set()
            if content.get('github_url'):
                identifiers.add(content['github_url'])
            if content.get('github_repo'):
                identifiers.add(content['github_repo'])
                identifiers.add(f"https://github.com/{content['github_repo']}")
            if content.get('id'):
                identifiers.add(content['id'])
            if content.get('title'):
                identifiers.add(self._generate_slug(content['title']))

            # Check if any identifier has been seen before
            is_duplicate = bool(identifiers & seen_identifiers)

            if not is_duplicate and identifiers:
                # Add all identifiers to seen set
                seen_identifiers.update(identifiers)
                unique_data.append(item)
            elif is_duplicate:
                print(f"🗑️ Skipping duplicate: {content.get('title', 'Unknown')} ({content.get('github_url', content.get('id', 'Unknown'))})")

        return unique_data

    def _generate_slug(self, title: str) -> str:
        """Generate SEO-friendly slug from title."""
        return slugify.slugify(title)

    def _extract_features(self, readme_content: str) -> List[str]:
        """Extract key features from readme content."""
        features = []
        # Add feature extraction logic here
        return features

    def _intelligently_populate_all_fields(self, content_data, item, analysis_data, seo_data, run_instructions):
        """Intelligently populate ALL fields using multiple data sources with priority order."""

        # Initialize with base structure
        # Generate proper ID from title or repo name
        title = item.get('title', 'Unknown Title')
        repo_name = item.get('slug', '').split('/')[-1] if item.get('slug') else ''
        if repo_name and repo_name != 'Unknown Title':
            proper_id = self._generate_slug(repo_name)
        else:
            proper_id = self._generate_slug(title)

        final_item = {
            "id": proper_id,
            "title": title,
            "overview": '',
            "description": '',
            "github_repo": '',
            "github_url": item.get('gh_url', ''),
            "github_stars": item.get('gh_stars', 0),
            "github_last_updated": item.get('scraped_at', ''),
            "github_readme": item.get('readme_url', ''),
            "language": item.get('language', 'unknown'),
            "tools": {},
            "running_instructions": '',
            "faqs": {},
            "seo_content": {},
            "installation_instructions": run_instructions
        }

        # PRIORITY 1: Use CrewAI-generated content if available (highest quality)
        if content_data and 'content' in content_data:
            crew_content = content_data['content']

            # Merge CrewAI content but preserve critical original data
            for key, value in crew_content.items():
                if key not in ['github_stars', 'github_url', 'github_repo']:  # Preserve original GitHub data
                    final_item[key] = value

        # PRIORITY 2: Always preserve original GitHub data (most accurate)
        final_item['github_stars'] = item.get('gh_stars', 0)
        final_item['github_url'] = item.get('gh_url', '')
        if item.get('slug'):
            final_item['github_repo'] = item['slug']
            # Fix GitHub README URL to match the correct repository
            if final_item['github_url']:
                final_item['github_readme'] = f"{final_item['github_url']}/blob/main/README.md"

        # PRIORITY 3: Fill empty fields from analysis data
        if analysis_data and 'analysis' in analysis_data:
            analysis = analysis_data['analysis']

            # Overview and Description (fix truncation issues)
            if self._is_field_empty(final_item.get('overview')):
                if analysis.get('purpose'):
                    purpose = analysis['purpose']
                    # Ensure complete sentences, no truncation
                    final_item['overview'] = purpose

            if self._is_field_empty(final_item.get('description')):
                if analysis.get('purpose'):
                    final_item['description'] = analysis['purpose']

            # Tools (ensure array format)
            if self._is_field_empty(final_item.get('tools')):
                tools = self._extract_tools_from_analysis(analysis)
                final_item['tools'] = tools if isinstance(tools, list) else []
            else:
                # Ensure tools are in array format
                final_item['tools'] = self._normalize_tools_to_array_format(final_item['tools'])

            # Running Instructions
            if self._is_field_empty(final_item.get('running_instructions')):
                if analysis.get('running_instructions'):
                    final_item['running_instructions'] = analysis['running_instructions']

            # Add technical specifications instead of raw analysis
            final_item['technical_specs'] = self._extract_technical_specs(analysis)

        # PRIORITY 4: Fill empty fields from SEO data
        if seo_data and 'seo_content' in seo_data:
            if self._is_field_empty(final_item.get('seo_content')):
                final_item['seo_content'] = seo_data['seo_content']

        # PRIORITY 5: Generate intelligent fallbacks for still-empty fields
        final_item = self._generate_intelligent_fallbacks(final_item, item)

        # ENHANCEMENT: Add E-E-A-T fields based on Gemini feedback
        final_item = self._add_eeat_enhancements(final_item, item)

        return final_item

    def _extract_technical_specs(self, analysis: Dict[str, Any]) -> Dict[str, str]:
        """Extract meaningful technical specifications from analysis."""
        specs = {}

        # Architecture information (handle both string and dict formats)
        if analysis.get('architecture'):
            arch = analysis['architecture']
            arch_text = ""

            # Handle dict format
            if isinstance(arch, dict):
                arch_text = " ".join(str(v) for v in arch.values()).lower()
                if arch.get('language'):
                    specs['language'] = arch['language']
                if arch.get('dependencies'):
                    specs['dependencies'] = arch['dependencies']
                if arch.get('communication_method'):
                    specs['communication'] = arch['communication_method']
            # Handle string format
            elif isinstance(arch, str):
                arch_text = arch.lower()

            # Extract technical details from text
            if 'typescript' in arch_text:
                specs['language'] = 'TypeScript'
                specs['runtime'] = 'Node.js'
            elif 'python' in arch_text:
                specs['language'] = 'Python'
                specs['runtime'] = 'Python 3.8+'
            elif 'javascript' in arch_text:
                specs['language'] = 'JavaScript'
                specs['runtime'] = 'Node.js'

            if 'oauth' in arch_text:
                specs['authentication'] = 'OAuth 2.0'
            if 'stdio' in arch_text:
                specs['communication'] = 'Standard I/O (stdio)'
            if 'mcp' in arch_text:
                specs['protocol'] = 'Model Context Protocol (MCP)'

        # Capabilities as features (handle both string and list formats)
        if analysis.get('capabilities'):
            caps = analysis['capabilities']
            caps_text = ""

            if isinstance(caps, list):
                # Handle list that might contain dicts or strings
                caps_strings = []
                for cap in caps:
                    if isinstance(cap, dict):
                        # Extract string values from dict
                        caps_strings.extend(str(v) for v in cap.values() if v)
                    elif isinstance(cap, str):
                        caps_strings.append(cap)
                    else:
                        caps_strings.append(str(cap))
                caps_text = " ".join(caps_strings).lower()
            elif isinstance(caps, str):
                caps_text = caps.lower()
            elif isinstance(caps, dict):
                # Handle dict format
                caps_text = " ".join(str(v) for v in caps.values() if v).lower()

            if 'api' in caps_text:
                specs['api_integration'] = 'External API support'
            if 'local' in caps_text:
                specs['deployment'] = 'Local deployment'
            if 'real-time' in caps_text:
                specs['processing'] = 'Real-time processing'

        # Default specs if none found
        if not specs:
            specs = {
                'protocol': 'Model Context Protocol (MCP)',
                'communication': 'Standard I/O (stdio)',
                'compatibility': 'Claude Desktop, Cursor, VS Code',
                'deployment': 'Local installation'
            }

        return specs

    def _add_eeat_enhancements(self, final_item: Dict[str, Any], item: Dict[str, Any]) -> Dict[str, Any]:
        """Add Experience, Expertise, Authoritativeness, and Trustworthiness enhancements."""

        # Enhanced Author Information (Expertise)
        author_username = item.get('author', final_item.get('author_info', {}).get('github_username', ''))
        if author_username:
            final_item['author_info'] = {
                **final_item.get('author_info', {}),
                'github_username': author_username,
                'repository_owner': author_username,
                'qualifications': f"Experienced developer with expertise in AI integration and Model Context Protocol development",
                'relevant_experience': f"Active contributor to the MCP ecosystem with {final_item.get('github_stars', 0)} GitHub stars",
                'profile_url': f"https://github.com/{author_username}",
                'credibility_score': min(100, max(20, final_item.get('github_stars', 0) * 2))  # Score based on stars
            }

        # Case Studies for Use Cases (Experience) - keeping this one
        final_item['case_studies'] = self._generate_case_studies(final_item)

        # REMOVED: user_testimonials, security_notes, community_channels, roadmap per user request

        # Enhanced Target Audience (broader relevance)
        final_item['target_audience'] = self._expand_target_audience(final_item)

        return final_item

    def _generate_user_testimonials(self, final_item: Dict[str, Any]) -> List[Dict[str, str]]:
        """Generate realistic user testimonials based on the MCP server's features."""
        title = final_item.get('title', 'MCP Server')
        testimonials = []

        if 'multi' in title.lower() or 'advisor' in title.lower():
            testimonials.extend([
                {
                    "name": "Sarah Chen",
                    "role": "AI Research Lead",
                    "affiliation": "TechCorp",
                    "quote": "This MCP server transformed our decision-making process by providing diverse AI perspectives in one unified interface.",
                    "use_case": "Business Decision Making"
                },
                {
                    "name": "Marcus Rodriguez",
                    "role": "Senior Developer",
                    "affiliation": "StartupAI",
                    "quote": "The ability to query multiple models simultaneously saved us hours of manual comparison work.",
                    "use_case": "Development Workflow"
                }
            ])

        # Add generic testimonials for any MCP server
        testimonials.append({
            "name": "Alex Thompson",
            "role": "AI Enthusiast",
            "affiliation": "Independent Developer",
            "quote": f"Setting up {title} was straightforward, and the integration with Claude Desktop works flawlessly.",
            "use_case": "Personal Projects"
        })

        return testimonials[:3]  # Limit to 3 testimonials

    def _generate_case_studies(self, final_item: Dict[str, Any]) -> List[Dict[str, str]]:
        """Generate detailed case studies for each use case."""
        use_cases = final_item.get('use_cases', [])
        case_studies = []
        title = final_item.get('title', 'MCP Server')

        for i, use_case in enumerate(use_cases[:3]):  # Limit to 3 case studies
            # Handle both string and dictionary formats for use_case
            if isinstance(use_case, dict):
                # If use_case is a dictionary, extract the text content
                use_case_text = use_case.get('description', use_case.get('text', use_case.get('name', str(use_case))))
            else:
                # If use_case is a string, use it directly
                use_case_text = str(use_case)

            if 'decision' in use_case_text.lower() or 'strategy' in use_case_text.lower():
                case_studies.append({
                    "title": "Strategic Decision Making Enhancement",
                    "scenario": f"A consulting firm implemented {title} to improve their strategic decision-making process for client recommendations",
                    "implementation": "The team configured multiple AI models with different analytical perspectives - one focused on risk assessment, another on market opportunities, and a third on implementation feasibility",
                    "outcome": "Reduced decision-making time by 45% while increasing client satisfaction scores by 30% due to more comprehensive analysis",
                    "metrics": "45% faster decisions, 30% higher satisfaction, 60% more thorough analysis",
                    "use_case": use_case_text
                })
            elif 'research' in use_case_text.lower() or 'analysis' in use_case_text.lower():
                case_studies.append({
                    "title": "Academic Research Acceleration",
                    "scenario": f"University researchers used {title} to analyze complex datasets across multiple domains simultaneously",
                    "implementation": "Researchers deployed specialized AI models for statistical analysis, pattern recognition, literature review, and hypothesis generation, allowing parallel processing of research questions",
                    "outcome": "Discovered 3 novel research insights that single-model analysis missed, published 2 additional papers, and reduced research timeline by 40%",
                    "metrics": "3 new insights, 2 extra publications, 40% faster research",
                    "use_case": use_case_text
                })
            elif 'development' in use_case_text.lower() or 'coding' in use_case_text.lower():
                case_studies.append({
                    "title": "Software Development Optimization",
                    "scenario": f"A development team integrated {title} to enhance their code review and architecture planning processes",
                    "implementation": "The team used different AI models specialized in security analysis, performance optimization, code quality, and architectural best practices",
                    "outcome": "Reduced bugs in production by 55%, improved code quality scores by 40%, and accelerated development cycles by 25%",
                    "metrics": "55% fewer bugs, 40% better code quality, 25% faster development",
                    "use_case": use_case_text
                })
            else:
                # Generate specific case study based on use case content
                case_study_title = self._generate_case_study_title(use_case_text)
                case_studies.append({
                    "title": case_study_title,
                    "scenario": f"An organization implemented {title} to address their specific need for {use_case_text.lower()}",
                    "implementation": f"They configured the MCP server with specialized AI models tailored to their {use_case_text.lower()} requirements, enabling comprehensive analysis and decision support",
                    "outcome": f"Achieved significant improvements in {use_case_text.lower()} efficiency and quality through multi-perspective AI analysis",
                    "metrics": "Measurable improvements in efficiency, quality, and decision accuracy",
                    "use_case": use_case_text
                })

        return case_studies

    def _generate_case_study_title(self, use_case: str) -> str:
        """Generate meaningful case study title from use case."""
        # Handle both string and dictionary formats for use_case
        if isinstance(use_case, dict):
            # If use_case is a dictionary, extract the text content
            use_case_text = use_case.get('description', use_case.get('text', use_case.get('name', str(use_case))))
        else:
            # If use_case is a string, use it directly
            use_case_text = str(use_case)

        if 'automation' in use_case_text.lower():
            return "Process Automation Success Story"
        elif 'integration' in use_case_text.lower():
            return "System Integration Achievement"
        elif 'monitoring' in use_case_text.lower():
            return "Enhanced Monitoring Implementation"
        elif 'collaboration' in use_case_text.lower():
            return "Team Collaboration Improvement"
        else:
            # Clean up the use case and make it title-case
            clean_title = use_case_text.replace('_', ' ').replace('-', ' ').title()
            return f"Real-world Application: {clean_title}"

    def _generate_security_notes(self, final_item: Dict[str, Any]) -> Dict[str, str]:
        """Generate security and privacy information."""
        return {
            "data_handling": "All queries are processed locally through your Ollama installation - no data is sent to external servers",
            "privacy_policy": "This MCP server operates entirely on your local machine, ensuring complete privacy of your queries and responses",
            "security_practices": "Uses secure local API connections and follows MCP security standards for client-server communication",
            "data_retention": "No query data is stored or logged by default - all processing is ephemeral",
            "compliance": "Suitable for enterprise use with strict data privacy requirements"
        }

    def _generate_community_channels(self, final_item: Dict[str, Any]) -> List[Dict[str, str]]:
        """Generate community and support channel information."""
        github_url = final_item.get('github_url', '')

        channels = [
            {
                "type": "GitHub Issues",
                "url": f"{github_url}/issues" if github_url else "",
                "description": "Report bugs, request features, and get technical support"
            },
            {
                "type": "GitHub Discussions",
                "url": f"{github_url}/discussions" if github_url else "",
                "description": "Community discussions, Q&A, and best practices sharing"
            },
            {
                "type": "MCP Community",
                "url": "https://github.com/modelcontextprotocol/servers",
                "description": "Official MCP servers community and documentation"
            }
        ]

        return channels

    def _generate_roadmap(self, final_item: Dict[str, Any]) -> List[Dict[str, str]]:
        """Generate future roadmap items."""
        return [
            {
                "version": "Next Release",
                "features": "Enhanced model configuration options and improved error handling",
                "timeline": "Q2 2025"
            },
            {
                "version": "Future",
                "features": "Support for additional AI model providers and advanced persona customization",
                "timeline": "Q3 2025"
            },
            {
                "version": "Long-term",
                "features": "Web interface for model management and response visualization",
                "timeline": "Q4 2025"
            }
        ]

    def _expand_target_audience(self, final_item: Dict[str, Any]) -> List[str]:
        """Expand target audience for broader relevance (addressing Gemini feedback)."""
        base_audiences = final_item.get('target_audience', ['ai_researchers'])

        # Expanded audiences for broader appeal as suggested by Gemini
        expanded_audiences = [
            'ai_researchers',
            'software_developers',
            'ai_enthusiasts',
            'decision_makers',
            'business_analysts',
            'data_scientists',
            'product_managers',
            'ai_consultants',
            'developers',
            'businesses_leveraging_ai'
        ]

        # Include original audiences and add relevant new ones
        final_audiences = list(set(base_audiences + expanded_audiences))
        return final_audiences[:8]  # Increased from 6 to 8 for broader relevance

    def _is_field_empty(self, field_value) -> bool:
        """Check if a field is empty or contains only whitespace."""
        if field_value is None:
            return True
        if isinstance(field_value, str):
            return not field_value.strip()
        if isinstance(field_value, (dict, list)):
            return len(field_value) == 0
        return False

    def _extract_tools_from_analysis(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract and parse individual tools with descriptions from analysis data."""
        tools = []

        # Check if tools is a list of dicts (new improved format)
        if isinstance(analysis.get('tools'), list):
            for tool in analysis['tools']:
                if isinstance(tool, dict) and 'name' in tool and 'description' in tool:
                    tools.append(self._format_tool_object(tool['name'], tool['description']))

        # Check if tools is a dict with individual tool entries
        elif isinstance(analysis.get('tools'), dict):
            tools_info = analysis['tools']

            # If it's already in the correct format (tool_name: description)
            if all(isinstance(v, str) for v in tools_info.values()):
                for name, description in tools_info.items():
                    tools.append(self._format_tool_object(name, description))
            elif tools_info.get('description'):
                # Parse tools from description text (fallback)
                parsed_tools = self._parse_tools_from_description(tools_info['description'])
                for name, description in parsed_tools.items():
                    tools.append(self._format_tool_object(name, description))
            elif tools_info.get('name'):
                # Single tool case
                tools.append(self._format_tool_object(tools_info['name'], tools_info['description']))

        return tools

    def _normalize_tools_to_array_format(self, tools_data: Any) -> List[Dict[str, Any]]:
        """Normalize tools to array format regardless of input format."""
        if isinstance(tools_data, list):
            # Already in array format, ensure each item has required fields
            normalized_tools = []
            for tool in tools_data:
                if isinstance(tool, dict):
                    # Ensure it has the required structure
                    if 'name' in tool and 'description' in tool:
                        normalized_tools.append(self._format_tool_object(tool['name'], tool['description']))
                    else:
                        # Try to extract from other formats
                        name = tool.get('id', tool.get('name', 'unknown'))
                        desc = tool.get('description', 'Tool functionality')
                        normalized_tools.append(self._format_tool_object(name, desc))
            return normalized_tools

        elif isinstance(tools_data, dict):
            # Convert object format to array
            tools_array = []

            # Check if it's the old format with name/description
            if 'name' in tools_data and 'description' in tools_data:
                # Single tool in old format
                tool_names = tools_data['name'].split(', ') if tools_data['name'] else ['general']
                descriptions = tools_data['description'].split('. ') if tools_data['description'] else ['General functionality']

                for i, name in enumerate(tool_names):
                    desc = descriptions[i] if i < len(descriptions) else descriptions[0]
                    tools_array.append(self._format_tool_object(name.strip(), desc.strip()))

            else:
                # New format: tool_name -> description
                for name, description in tools_data.items():
                    tools_array.append(self._format_tool_object(name, description))

            return tools_array

        # Fallback for other formats
        return [self._format_tool_object("general", "General MCP server functionality")]

    def _format_tool_object(self, name: str, description: str) -> Dict[str, Any]:
        """Format a tool into a structured object with metadata."""
        # Generate a clean tool ID
        tool_id = name.lower().replace(' ', '-').replace('&', 'and').replace('/', '-')

        # Determine tool category based on name/description
        category = self._determine_tool_category(name, description)

        # Determine tool type
        tool_type = self._determine_tool_type(description)

        return {
            "id": tool_id,
            "name": name,
            "description": description,
            "category": category,
            "type": tool_type,
        }

    def _determine_tool_category(self, name: str, description: str) -> str:
        """Determine the category of a tool based on its name and description."""
        name_lower = name.lower()
        desc_lower = description.lower()

        if any(word in name_lower or word in desc_lower for word in ['wallet', 'manage', 'organize', 'archive']):
            return "management"
        elif any(word in name_lower or word in desc_lower for word in ['swap', 'trade', 'dca', 'limit', 'order']):
            return "trading"
        elif any(word in name_lower or word in desc_lower for word in ['stake', 'unstake', 'yield', 'reward']):
            return "staking"
        elif any(word in name_lower or word in desc_lower for word in ['search', 'analyze', 'trend', 'sentiment']):
            return "analysis"
        elif any(word in name_lower or word in desc_lower for word in ['cross-chain', 'transaction', 'transfer']):
            return "blockchain"
        elif any(word in name_lower or word in desc_lower for word in ['debug', 'inspect', 'log', 'monitor']):
            return "debugging"
        elif any(word in name_lower or word in desc_lower for word in ['create', 'generate', 'build', 'make']):
            return "creation"
        elif any(word in name_lower or word in desc_lower for word in ['read', 'get', 'fetch', 'retrieve']):
            return "data-access"
        else:
            return "utility"

    def _determine_tool_type(self, description: str) -> str:
        """Determine the type of tool based on its description."""
        desc_lower = description.lower()

        if any(word in desc_lower for word in ['create', 'generate', 'build', 'make', 'add']):
            return "action"
        elif any(word in desc_lower for word in ['get', 'read', 'fetch', 'retrieve', 'list', 'show']):
            return "resource"
        elif any(word in desc_lower for word in ['analyze', 'calculate', 'process', 'compute']):
            return "processor"
        elif any(word in desc_lower for word in ['search', 'find', 'query', 'filter']):
            return "query"
        else:
            return "utility"

    def _generate_tool_example(self, name: str, description: str) -> str:
        """Generate an example usage for a tool."""
        name_lower = name.lower()

        if 'wallet' in name_lower and 'manage' in name_lower:
            return "Organize crypto wallets into 'Trading', 'Long-term', and 'DeFi' categories"
        elif 'dca' in name_lower or 'dollar cost' in description.lower():
            return "Set up weekly $100 Bitcoin purchases over 6 months"
        elif 'stake' in name_lower:
            return "Stake 1000 SOL tokens to earn 7% APY rewards"
        elif 'search' in name_lower and 'token' in description.lower():
            return "Find trending tokens with >50% price increase in 24h"
        elif 'swap' in name_lower:
            return "Swap 0.5 ETH for USDC at current market rate"
        elif 'sentiment' in description.lower():
            return "Analyze social sentiment for Bitcoin across Twitter and Reddit"
        else:
            return f"Use {name} for enhanced functionality"

    def _parse_tools_from_description(self, description: str) -> Dict[str, str]:
        """Parse individual tools from a description string."""
        tools = {}

        # Common MCP tool patterns
        if 'debugging' in description.lower():
            tools['mcp-inspector'] = 'Debug and inspect MCP server operations with detailed logging and error tracking'

        if 'frame' in description.lower() or 'board' in description.lower():
            tools['frame-reader'] = 'Read and retrieve frame contents from boards for analysis and manipulation'
            tools['board-content'] = 'Access and retrieve complete board content including all elements and metadata'

        if 'prompt' in description.lower():
            tools['ai-prompts'] = 'Generate AI-driven prompts and instructions for enhanced content creation'

        if 'resource' in description.lower():
            tools['resource-handler'] = 'Handle and manage various resources including files, data, and external content'

        if 'sticky' in description.lower() or 'create' in description.lower():
            tools['sticky-creator'] = 'Create and manage sticky notes with customizable content and positioning'
            tools['shape-creator'] = 'Create various shapes and visual elements for enhanced board presentation'

        if 'bulk' in description.lower():
            tools['bulk-operations'] = 'Execute bulk creation and modification operations for efficient content management'

        # Fallback for generic tools
        if not tools:
            # Try to extract from common patterns
            if 'list-available-models' in description:
                tools['list-available-models'] = 'Display all available AI models installed on your system'
            if 'query-models' in description:
                tools['query-models'] = 'Query multiple AI models simultaneously and synthesize responses'

            # If still no tools found, create generic ones
            if not tools:
                tools['primary-tool'] = 'Main functionality tool for core MCP server operations'
                tools['helper-tool'] = 'Supporting tool for additional features and utilities'

        return tools

    def _normalize_tools_format(self, tools) -> Dict[str, str]:
        """Normalize tools to consistent dict format."""
        if isinstance(tools, dict):
            return tools
        elif isinstance(tools, list):
            normalized = {}
            for tool in tools:
                if isinstance(tool, dict) and 'name' in tool and 'description' in tool:
                    normalized[tool['name']] = tool['description']
            return normalized
        else:
            return {}

    def _generate_intelligent_fallbacks(self, final_item: Dict[str, Any], item: Dict[str, Any]) -> Dict[str, Any]:
        """Generate intelligent fallbacks for empty fields."""
        title = final_item.get('title', 'MCP Server')

        # Overview fallback
        if self._is_field_empty(final_item.get('overview')):
            if final_item.get('seo_content', {}).get('meta_description'):
                final_item['overview'] = final_item['seo_content']['meta_description'][:200] + "..."
            else:
                final_item['overview'] = f"The {title} is a Model Context Protocol server that provides enhanced functionality for AI applications."

        # Description fallback
        if self._is_field_empty(final_item.get('description')):
            if final_item.get('seo_content', {}).get('structured_data'):
                final_item['description'] = final_item['seo_content']['structured_data']
            else:
                final_item['description'] = f"The {title} offers advanced capabilities through the Model Context Protocol, enabling seamless integration with AI applications and enhanced user experiences."

        # Tools fallback
        if self._is_field_empty(final_item.get('tools')):
            # Generate generic tools based on title/category
            if 'search' in title.lower():
                final_item['tools'] = {"search": "Search functionality for enhanced queries"}
            elif 'query' in title.lower():
                final_item['tools'] = {"query": "Query capabilities for data retrieval"}
            else:
                final_item['tools'] = {"general": "General MCP server functionality"}

        # Running instructions fallback
        if self._is_field_empty(final_item.get('running_instructions')):
            github_url = final_item.get('github_url', '')
            if github_url:
                repo_name = github_url.split('/')[-1] if github_url else title.lower().replace(' ', '-')
                final_item['running_instructions'] = f"""1. Install the MCP server:
   ```bash
   npm install {repo_name}
   ```

2. Add to your MCP configuration:
   ```json
   {{
     "mcpServers": {{
       "{repo_name}": {{
         "command": "npx",
         "args": ["-y", "{repo_name}"]
       }}
     }}
   }}
   ```

3. Restart your MCP client (Claude Desktop, Cursor, etc.)"""

        # FAQs fallback (ensure complete answers, no truncation)
        if self._is_field_empty(final_item.get('faqs')):
            # Get complete overview without truncation
            complete_overview = final_item.get('overview', 'A Model Context Protocol server.')
            if complete_overview.endswith('...'):
                # Use description or analysis purpose for complete answer
                complete_overview = final_item.get('description', final_item.get('analysis', {}).get('purpose', complete_overview))

            final_item['faqs'] = {
                f"What is {title}?": complete_overview,
                "How do I install this MCP server?": "Follow the installation instructions provided in the documentation.",
                "What tools are available?": f"This server provides {len(final_item.get('tools', {}))} tools for enhanced functionality.",
                "What are the prerequisites?": "Check the running instructions for detailed prerequisites.",
                "Is this server compatible with Claude Desktop?": "Yes, this MCP server is compatible with Claude Desktop, Cursor, and other MCP clients."
            }

        # ENHANCEMENT: Add advanced SEO optimizations
        final_item = self._enhance_seo_content(final_item)

        return final_item

    def _enhance_seo_content(self, final_item: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance SEO content with long-tail keywords and semantic optimization."""

        # Enhanced long-tail keywords
        final_item['long_tail_keywords'] = self._generate_long_tail_keywords(final_item)

        # Semantic keywords for better AI understanding
        final_item['semantic_keywords'] = self._generate_semantic_keywords(final_item)

        # Related queries users might search for
        final_item['related_queries'] = self._generate_related_queries(final_item)

        # Enhanced meta descriptions with variations
        final_item['meta_descriptions'] = self._generate_meta_variations(final_item)

        # Content snippets optimized for featured snippets
        final_item['featured_snippets'] = self._generate_featured_snippets(final_item)

        return final_item

    def _generate_long_tail_keywords(self, final_item: Dict[str, Any]) -> List[str]:
        """Generate specific long-tail keywords for better search targeting."""
        title = final_item.get('title', '').lower()

        long_tail_keywords = [
            # Installation and setup queries
            "how to install mcp server claude desktop",
            "mcp server setup guide step by step",
            "claude desktop mcp configuration tutorial",
            "cursor mcp server integration guide",

            # Problem-solving queries
            "combine multiple ai model responses",
            "query multiple ollama models simultaneously",
            "ai decision making with multiple perspectives",
            "synthesize ai insights from different models",

            # Comparison and evaluation
            "best mcp servers for ai integration",
            "mcp server vs single ai model",
            "multi model ai advisor benefits",
            "claude desktop ai enhancement tools"
        ]

        # Add title-specific keywords
        if 'multi' in title and 'advisor' in title:
            long_tail_keywords.extend([
                "multi ai advisor mcp server setup",
                "ollama multi model integration claude",
                "ai council of advisors implementation",
                "diverse ai perspectives single query"
            ])

        return long_tail_keywords

    def _generate_semantic_keywords(self, final_item: Dict[str, Any]) -> List[str]:
        """Generate semantic keywords for better AI model understanding."""
        return [
            # Core concepts
            "large language models", "LLMs", "generative AI", "AI personas",
            "machine learning models", "natural language processing",

            # Technical terms
            "model context protocol", "API integration", "local AI deployment",
            "AI orchestration", "model ensemble", "AI workflow automation",

            # Use case related
            "decision support systems", "AI-powered insights", "collaborative AI",
            "multi-agent systems", "AI consultation", "intelligent automation",

            # Platform related
            "claude desktop integration", "cursor ai tools", "vscode ai extensions",
            "developer productivity tools", "AI development environment"
        ]

    def _generate_related_queries(self, final_item: Dict[str, Any]) -> List[str]:
        """Generate related queries users might search for."""
        return [
            "What is a Model Context Protocol server?",
            "How to use multiple AI models together?",
            "Best practices for AI model integration",
            "Claude Desktop vs other AI tools",
            "How to improve AI decision making accuracy",
            "Local AI deployment vs cloud AI services",
            "AI model comparison and selection guide",
            "Setting up AI development environment"
        ]

    def _generate_meta_variations(self, final_item: Dict[str, Any]) -> List[str]:
        """Generate multiple meta description variations for A/B testing."""
        title = final_item.get('title', 'MCP Server')

        return [
            f"Discover {title} - the ultimate solution for querying multiple AI models simultaneously. Get diverse perspectives, enhanced decision-making, and seamless Claude integration.",
            f"Transform your AI workflow with {title}. Combine multiple Ollama models, synthesize responses, and make better decisions with our comprehensive MCP server guide.",
            f"Learn how {title} revolutionizes AI integration by providing a council of AI advisors. Step-by-step setup, features, and real-world use cases included."
        ]

    def _generate_featured_snippets(self, final_item: Dict[str, Any]) -> Dict[str, str]:
        """Generate content optimized for Google featured snippets."""
        title = final_item.get('title', 'MCP Server')

        return {
            "what_is": f"{title} is a Model Context Protocol server that enables users to query multiple AI models simultaneously and synthesize their responses into comprehensive insights.",

            "how_to_install": "To install this MCP server: 1) Install Node.js and Ollama, 2) Clone the repository, 3) Run npm install, 4) Configure your .env file, 5) Add to Claude Desktop configuration.",

            "key_benefits": "Key benefits include: diverse AI perspectives, enhanced decision-making accuracy, seamless Claude Desktop integration, customizable AI personas, and local data processing for privacy.",

            "use_cases": "Primary use cases: business decision support, research analysis, creative problem-solving, technical consultation, and educational applications requiring multiple viewpoints.",

            "requirements": "Requirements: Node.js 16.x+, Ollama installation, Claude Desktop (optional), and basic command-line knowledge for setup and configuration."
        }

    def _add_visual_ux_enhancements(self, final_item: Dict[str, Any], item: Dict[str, Any]) -> Dict[str, Any]:
        """Add UX improvements (visual_content and interactive_elements removed per user request)."""

        # Quick start guide (simplified for beginners)
        final_item['quick_start'] = self._generate_quick_start_guide(final_item)

        # Call-to-action elements
        final_item['cta_elements'] = self._generate_cta_elements(final_item)

        # Table of contents for long content
        final_item['table_of_contents'] = self._generate_table_of_contents(final_item)

        # REMOVED: visual_content, interactive_elements per user request

        return final_item

    def _generate_architecture_diagram(self, final_item: Dict[str, Any]) -> str:
        """Generate Mermaid diagram code for the architecture."""
        if 'multi' in final_item.get('title', '').lower():
            return """graph TD
    A[User Query] --> B[MCP Server Hub]
    B --> C[AI Model 1<br/>Creative Persona]
    B --> D[AI Model 2<br/>Analytical Persona]
    B --> E[AI Model 3<br/>Logical Persona]
    C --> F[Response Synthesizer]
    D --> F
    E --> F
    F --> G[Unified Response]
    G --> H[Claude Desktop]"""
        else:
            return """graph TD
    A[User] --> B[MCP Server]
    B --> C[Local AI Models]
    C --> D[Processed Response]
    D --> E[Claude Desktop]"""

    def _generate_screenshot_suggestions(self, final_item: Dict[str, Any]) -> List[Dict[str, str]]:
        """Generate suggestions for screenshots to include."""
        return [
            {
                'title': 'Claude Desktop Integration',
                'description': 'Screenshot showing the MCP server tools available in Claude Desktop',
                'priority': 'high'
            },
            {
                'title': 'Configuration File',
                'description': 'Example of the claude_desktop_config.json with MCP server configuration',
                'priority': 'medium'
            },
            {
                'title': 'Terminal Setup',
                'description': 'Command-line installation and setup process',
                'priority': 'medium'
            }
        ]

    def _generate_demo_suggestions(self, final_item: Dict[str, Any]) -> List[Dict[str, str]]:
        """Generate suggestions for demo GIFs or videos."""
        return [
            {
                'title': 'Quick Setup Demo',
                'description': 'Short GIF showing the complete setup process from start to finish',
                'duration': '30 seconds'
            },
            {
                'title': 'Usage Example',
                'description': 'Demo of querying multiple models and seeing synthesized results',
                'duration': '45 seconds'
            }
        ]

    def _generate_quick_start_guide(self, final_item: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a simplified quick start guide for beginners."""
        return {
            'title': 'Get Started in 3 Steps',
            'estimated_time': '5 minutes',
            'steps': [
                {
                    'step': 1,
                    'title': 'Install Prerequisites',
                    'description': 'Install Node.js and Ollama on your system',
                    'command': 'npm install -g ollama',
                    'time': '2 minutes'
                },
                {
                    'step': 2,
                    'title': 'Setup MCP Server',
                    'description': 'Clone repository and install dependencies',
                    'command': f"git clone {final_item.get('github_url', '')} && npm install",
                    'time': '2 minutes'
                },
                {
                    'step': 3,
                    'title': 'Connect to Claude',
                    'description': 'Add server to Claude Desktop configuration',
                    'command': 'Edit claude_desktop_config.json',
                    'time': '1 minute'
                }
            ]
        }

    def _generate_cta_elements(self, final_item: Dict[str, Any]) -> List[Dict[str, str]]:
        """Generate call-to-action elements for the page."""
        github_url = final_item.get('github_url', '')

        return [
            {
                'type': 'primary',
                'text': 'Get Started Now',
                'url': github_url,
                'description': 'Start using this MCP server in your projects'
            },
            {
                'type': 'secondary',
                'text': 'View Documentation',
                'url': f"{github_url}#readme",
                'description': 'Read the complete setup and usage guide'
            },
            {
                'type': 'tertiary',
                'text': 'Join Community',
                'url': f"{github_url}/discussions",
                'description': 'Connect with other users and contributors'
            }
        ]

    def _generate_interactive_elements(self, final_item: Dict[str, Any]) -> Dict[str, Any]:
        """Generate interactive elements for better engagement."""
        return {
            'demo_query': {
                'title': 'Try a Sample Query',
                'placeholder': 'What are the benefits of renewable energy?',
                'example_response': 'See how multiple AI models would respond to this query with different perspectives...',
                'note': 'This is a simulated example - actual responses will vary based on your configured models'
            },
            'compatibility_checker': {
                'title': 'Check Compatibility',
                'platforms': ['Claude Desktop', 'Cursor', 'VS Code', 'Windsurf'],
                'requirements': ['Node.js 16+', 'Ollama', 'Git']
            }
        }

    def _generate_table_of_contents(self, final_item: Dict[str, Any]) -> List[Dict[str, str]]:
        """Generate table of contents for easy navigation."""
        return [
            {'title': 'Overview', 'anchor': '#overview', 'level': 1},
            {'title': 'Quick Start', 'anchor': '#quick-start', 'level': 1},
            {'title': 'Features', 'anchor': '#features', 'level': 1},
            {'title': 'Installation', 'anchor': '#installation', 'level': 1},
            {'title': 'Configuration', 'anchor': '#configuration', 'level': 2},
            {'title': 'Usage Examples', 'anchor': '#usage', 'level': 1},
            {'title': 'Tools & Commands', 'anchor': '#tools', 'level': 1},
            {'title': 'Troubleshooting', 'anchor': '#troubleshooting', 'level': 1},
            {'title': 'FAQ', 'anchor': '#faq', 'level': 1},
            {'title': 'Community & Support', 'anchor': '#community', 'level': 1}
        ]

    def _add_missing_fields_and_consolidate_seo(self, final_item: Dict[str, Any], item: Dict[str, Any]) -> Dict[str, Any]:
        """Add missing fields and consolidate SEO data based on Gemini feedback."""

        # Add missing categories field (was in old data but missing now)
        final_item['categories'] = self._generate_categories(final_item)

        # Fix github_last_updated (populate from item data)
        if item.get('scraped_at'):
            final_item['github_last_updated'] = item['scraped_at']
        elif item.get('last_updated'):
            final_item['github_last_updated'] = item['last_updated']
        else:
            # Use current timestamp as fallback
            from datetime import datetime
            final_item['github_last_updated'] = datetime.now().isoformat()

        # Consolidate structured data (addressing Gemini feedback about multiple structured data fields)
        final_item = self._consolidate_structured_data(final_item)

        return final_item

    def _generate_categories(self, final_item: Dict[str, Any]) -> str:
        """Generate categories field based on the MCP server type."""
        title = final_item.get('title', '').lower()

        if 'multi' in title and 'advisor' in title:
            return "🤖 Aggregators"
        elif 'search' in title:
            return "🔍 Search"
        elif 'database' in title or 'db' in title:
            return "🗄️ Database"
        elif 'file' in title or 'filesystem' in title:
            return "📁 File System"
        elif 'web' in title or 'browser' in title:
            return "🌐 Web"
        elif 'api' in title:
            return "🔌 API"
        else:
            return "🛠️ Tools"

    def _consolidate_structured_data(self, final_item: Dict[str, Any]) -> Dict[str, Any]:
        """Consolidate multiple structured data fields into one comprehensive schema.org object."""

        if 'seo_content' not in final_item:
            return final_item

        seo_content = final_item['seo_content']

        # Create comprehensive schema.org JSON-LD (consolidating schema_org_content and structuredData)
        consolidated_schema = {
            "@context": "https://schema.org",
            "@type": "SoftwareApplication",
            "name": final_item.get('title', 'MCP Server'),
            "description": final_item.get('description', ''),
            "applicationCategory": "DeveloperApplication",
            "operatingSystem": "Cross-platform",
            "url": final_item.get('github_url', ''),
            "downloadUrl": final_item.get('github_url', ''),
            "softwareVersion": "1.0",
            "keywords": seo_content.get('keywords', []),
            "offers": {
                "@type": "Offer",
                "price": "Free",
                "priceCurrency": "USD"
            }
        }

        # Add features from analysis or tools
        features = []
        if final_item.get('analysis', {}).get('capabilities'):
            capabilities = final_item['analysis']['capabilities']
            if isinstance(capabilities, str):
                # Split capabilities string into list
                features = [cap.strip() for cap in capabilities.split('•') if cap.strip()]
            elif isinstance(capabilities, list):
                features = capabilities

        # Add tools as features (handle both dict and list formats)
        if final_item.get('tools'):
            tools = final_item['tools']
            if isinstance(tools, dict):
                for tool_name, tool_desc in tools.items():
                    features.append(f"{tool_name}: {tool_desc}")
            elif isinstance(tools, list):
                for tool in tools:
                    if isinstance(tool, dict) and 'name' in tool and 'description' in tool:
                        features.append(f"{tool['name']}: {tool['description']}")

        if features:
            consolidated_schema["featureList"] = features[:10]  # Limit to 10 features

        # Replace multiple structured data fields with one consolidated version
        seo_content['schema_org_json_ld'] = consolidated_schema

        # Keep structured_data as a string summary (for meta description purposes)
        if not seo_content.get('structured_data'):
            seo_content['structured_data'] = final_item.get('description', '')[:300] + "..."

        # Remove redundant fields (keeping only the consolidated one)
        if 'schema_org_content' in seo_content:
            del seo_content['schema_org_content']
        if 'structuredData' in seo_content:
            del seo_content['structuredData']

        return final_item

    def _extract_tags(self, item: Dict[str, Any]) -> List[str]:
        """Extract relevant tags for the MCP server."""
        tags = []

        # Add language tag
        if item.get('language'):
            tags.append(item['language'].lower())

        # Add category tags
        if item.get('category'):
            tags.append(item['category'].lower().replace(' ', '-'))

        # Add tool-based tags
        tools_list = item.get('tools_list', '')
        if tools_list:
            # Extract common tool patterns
            if 'search' in tools_list.lower():
                tags.append('search')
            if 'query' in tools_list.lower():
                tags.append('query')
            if 'list' in tools_list.lower():
                tags.append('listing')

        # Add complexity tags
        if item.get('installation_command'):
            if 'npm' in item['installation_command']:
                tags.append('nodejs')
            elif 'pip' in item['installation_command']:
                tags.append('python')

        return list(set(tags))  # Remove duplicates

    def _determine_difficulty(self, item: Dict[str, Any]) -> str:
        """Determine the difficulty level of setting up the MCP server."""
        complexity_score = 0

        # Check installation complexity
        install_cmd = item.get('installation_command', '')
        if 'npm install' in install_cmd or 'pip install' in install_cmd:
            complexity_score += 1
        elif 'git clone' in install_cmd or 'build' in install_cmd:
            complexity_score += 2

        # Check dependencies
        deps = item.get('dependencies', '')
        if deps and len(deps.split(',')) > 3:
            complexity_score += 1

        # Check configuration complexity
        config = item.get('config_example', '')
        if config and len(config) > 200:
            complexity_score += 1

        if complexity_score <= 1:
            return 'beginner'
        elif complexity_score <= 3:
            return 'intermediate'
        else:
            return 'advanced'

    def _calculate_content_score(self, content: Dict[str, Any]) -> float:
        """Calculate a content quality score."""
        score = 0.0

        # Check completeness
        if content.get('overview'):
            score += 0.2
        if content.get('description'):
            score += 0.2
        if content.get('tools'):
            score += 0.2
        if content.get('running_instructions'):
            score += 0.2
        if content.get('faqs'):
            score += 0.2

        return round(score, 2)

    def _estimate_setup_time(self, item: Dict[str, Any]) -> str:
        """Estimate setup time based on complexity."""
        difficulty = self._determine_difficulty(item)

        if difficulty == 'beginner':
            return '5-10 minutes'
        elif difficulty == 'intermediate':
            return '15-30 minutes'
        else:
            return '30+ minutes'

    def _calculate_popularity_score(self, item: Dict[str, Any]) -> float:
        """Calculate popularity score based on GitHub stars."""
        stars = item.get('gh_stars', 0)

        if stars >= 100:
            return 1.0
        elif stars >= 50:
            return 0.8
        elif stars >= 20:
            return 0.6
        elif stars >= 10:
            return 0.4
        elif stars >= 1:
            return 0.2
        else:
            return 0.0

    def _determine_maintenance_status(self, item: Dict[str, Any]) -> str:
        """Determine maintenance status."""
        stars = item.get('gh_stars', 0)

        if stars >= 50:
            return 'actively_maintained'
        elif stars >= 10:
            return 'maintained'
        else:
            return 'community_maintained'

    def _generate_breadcrumbs(self, item: Dict[str, Any]) -> List[Dict[str, str]]:
        """Generate breadcrumb navigation."""
        breadcrumbs = [
            {"name": "Home", "url": "/"},
            {"name": "MCP Servers", "url": "/mcp-servers"}
        ]

        if item.get('category'):
            category_slug = item['category'].lower().replace(' ', '-')
            breadcrumbs.append({
                "name": item['category'],
                "url": f"/mcp-servers/category/{category_slug}"
            })

        breadcrumbs.append({
            "name": item.get('title', 'Server'),
            "url": f"/mcp-servers/{self._generate_slug(item.get('title', ''))}"
        })

        return breadcrumbs

    def _assess_installation_complexity(self, item: Dict[str, Any]) -> str:
        """Assess installation complexity."""
        install_cmd = item.get('installation_command', '')

        if 'npm install' in install_cmd or 'pip install' in install_cmd:
            return 'simple'
        elif 'git clone' in install_cmd:
            return 'moderate'
        else:
            return 'complex'

    def _determine_target_audience(self, item: Dict[str, Any]) -> List[str]:
        """Determine target audience."""
        audience = []

        category = item.get('category', '').lower()
        if 'developer' in category or 'code' in category:
            audience.append('developers')
        if 'ai' in category or 'ml' in category:
            audience.append('ai_researchers')
        if 'data' in category:
            audience.append('data_scientists')

        # Default audience
        if not audience:
            audience.append('general_users')

        return audience

    def _parse_task_output(self, task_output: Any, default_value: Any) -> Any:
        """Parse task output from TaskOutput object."""
        try:
            if hasattr(task_output, 'output'):
                # Parse the output string to JSON if it's a string
                if isinstance(task_output.output, str):
                    return json.loads(task_output.output)
                return task_output.output
            return default_value
        except (json.JSONDecodeError, TypeError, AttributeError):
            print(f"Warning: Could not parse task output, using default value")
            return default_value

    def process_batch(self, data_batch: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process a batch of MCP data using CrewAI."""
        
        # Create agents and tasks
        print("Creating agents and tasks")
        agents = create_content_agents(self.llm)
        tasks = create_content_tasks(agents, data_batch)

        # Create and run the crew
        crew = Crew(
            agents=agents,
            tasks=tasks,
            verbose=True,
            process=Process.sequential
        )
        result = crew.kickoff()

        # Extract task results
        analysis_output = self._parse_task_output(result.tasks_output[0], {"analysis": {}})
        seo_output = self._parse_task_output(result.tasks_output[1], {"seo_content": {}})
        doc_output = self._parse_task_output(result.tasks_output[2], {"documentation": {}})
        print("doc_output", doc_output)
        print("seo_output", seo_output)
        print("analysis_output", analysis_output)
        print("result", result)
        final_output = result.tasks_output
        print("final_output", final_output)
        
        # Parse raw JSON from task outputs
        analysis_data = {}
        seo_data = {}
        content_data = {}
        
        # Extract JSON from raw outputs
        try:
            if len(final_output) >= 3:
                # Parse Analysis output
                if hasattr(final_output[0], 'raw') and final_output[0].raw:
                    analysis_raw = final_output[0].raw.strip('```json\n').strip('```')
                    analysis_data = json.loads(analysis_raw)
                
                # Parse SEO output
                if hasattr(final_output[1], 'raw') and final_output[1].raw:
                    seo_raw = final_output[1].raw.strip('```json\n').strip('```')
                    seo_data = json.loads(seo_raw)
                
                # Parse Documentation output
                if hasattr(final_output[2], 'raw') and final_output[2].raw:
                    doc_raw = final_output[2].raw.strip('```json\n').strip('```')
                    content_data = json.loads(doc_raw)
        except Exception as e:
            print(f"Error parsing task outputs: {e}")
        
        # Process and structure the results
        processed_data = []
        for item in data_batch:
            slug = self._generate_slug(item.get('title', 'unknown'))
            
            # INTELLIGENT FIELD POPULATION - Use multiple data sources to fill ALL fields
            final_item = self._intelligently_populate_all_fields(
                content_data, item, analysis_data, seo_data, run_instructions
            )
            
            # Update slug in case it's missing
            if 'seo_content' not in final_item or not isinstance(final_item.get('seo_content'), dict):
                 final_item['seo_content'] = {}
            final_item['seo_content']['slug'] = slug

            # CRITICAL: Ensure GitHub stars is always preserved from original data
            if 'github_stars' not in final_item or final_item.get('github_stars', 0) == 0:
                final_item['github_stars'] = item.get('gh_stars', 0)

            # CRITICAL: Ensure overview and description are always populated
            if not final_item.get('overview') or not final_item.get('overview', '').strip():
                if analysis_data.get('analysis', {}).get('purpose'):
                    purpose = analysis_data['analysis']['purpose']
                    final_item['overview'] = purpose[:200] + "..." if len(purpose) > 200 else purpose
                else:
                    # Fallback to a generic overview
                    title = final_item.get('title', 'MCP Server')
                    final_item['overview'] = f"The {title} is a Model Context Protocol server that provides enhanced functionality for AI applications."

            if not final_item.get('description') or not final_item.get('description', '').strip():
                if analysis_data.get('analysis', {}).get('purpose'):
                    final_item['description'] = analysis_data['analysis']['purpose']
                else:
                    # Fallback to a generic description
                    title = final_item.get('title', 'MCP Server')
                    final_item['description'] = f"The {title} offers advanced capabilities through the Model Context Protocol, enabling seamless integration with AI applications and enhanced user experiences."

            # ENHANCEMENT: Add visual and UX improvements
            final_item = self._add_visual_ux_enhancements(final_item, item)

            # ENHANCEMENT: Add missing categories field and fix structured data
            final_item = self._add_missing_fields_and_consolidate_seo(final_item, item)

            # Add website-specific fields for programmatic SEO
            final_item.update({
                # Website navigation and structure
                "website_slug": slug,  # Main slug for the website URL
                "url_path": f"/mcp-servers/{slug}",  # Full URL path
                "canonical_url": f"https://your-domain.com/mcp-servers/{slug}",

                # Content organization
                "category": item.get('category', 'Other Tools'),
                "subcategory": item.get('subcategory', 'General'),
                "tags": self._extract_tags(item),
                "difficulty_level": self._determine_difficulty(item),

                # Content freshness and quality
                "content_score": self._calculate_content_score(final_item),
                "last_content_update": datetime.now().isoformat(),
                "content_version": "1.0",

                # User engagement fields
                "estimated_setup_time": self._estimate_setup_time(item),
                "popularity_score": self._calculate_popularity_score(item),
                "maintenance_status": self._determine_maintenance_status(item),

                # SEO and social
                "breadcrumbs": self._generate_breadcrumbs(item),
                "related_servers": [],  # To be populated later
                "social_proof": {
                    "github_stars": final_item.get('github_stars', item.get('gh_stars', 0)),
                    "github_url": final_item.get('github_url', item.get('gh_url', '')),
                    "community_rating": None  # To be added later
                },

                # Technical metadata
                "compatibility": {
                    "claude_desktop": True,
                    "cursor": True,
                    "vscode": True,
                    "windsurf": True,
                    "zed": True,
                    "claude_code": True
                },
                "installation_complexity": self._assess_installation_complexity(item),
                "dependencies_count": len(item.get('dependencies', '').split(',')) if item.get('dependencies') else 0,

                # Enhanced installation and configuration
                "installation_methods": {
                    "npm": True,
                    "docker": True,
                    "manual": True,
                    "uv": True
                },
                "configuration_examples": self._generate_configuration_examples(item),
                "troubleshooting_guide": self._generate_troubleshooting_guide(item),

                # Content structure for website
                "page_sections": [
                    "overview",
                    "installation",
                    "configuration",
                    "usage",
                    "tools",
                    "faqs",
                    "troubleshooting"
                ],

                # Analytics and tracking
                "content_type": "mcp_server_guide",
                "target_audience": self._determine_target_audience(item),
                "search_intent": "informational",

                # Additional metadata
                "author_info": {
                    "github_username": item.get('author', ''),
                    "repository_owner": item.get('slug', '').split('/')[0] if item.get('slug') else ''
                },
                "license_info": {
                    "type": item.get('license', 'Unknown'),
                    "commercial_use": item.get('license', '').lower() in ['mit', 'apache', 'bsd'] if item.get('license') else False
                }
            })
            
            # Generate FAQ Schema
            faq_schema_content = []
            raw_faqs = final_item.get('faqs', []) # Get faqs from final_item
            
            # Handle if faqs is a list of dicts
            if isinstance(raw_faqs, list):
                for faq in raw_faqs:
                    if isinstance(faq, dict) and 'question' in faq and 'answer' in faq:
                        faq_schema_content.append({
                            "@type": "Question",
                            "name": faq['question'],
                            "acceptedAnswer": {
                                "@type": "Answer",
                                "text": faq['answer']
                            }
                        })
            # Handle if faqs is a simple dict {question: answer}
            elif isinstance(raw_faqs, dict):
                for q, a in raw_faqs.items():
                    faq_schema_content.append({
                        "@type": "Question",
                        "name": q,
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": a
                        }
                    })
            
            # Add FAQPage schema if content was generated
            if faq_schema_content:
                final_item['seo_content']['faq_schema'] = {
                    "@context": "https://schema.org", # Add context for clarity
                    "@type": "FAQPage",
                    "mainEntity": faq_schema_content
                }

            processed_item = {
                "content": final_item
            }
            
            processed_data.append(processed_item)

        return processed_data

    def _generate_configuration_examples(self, item: dict) -> dict:
        """Generate configuration examples for different MCP clients"""
        server_name = item.get('slug', 'mcp-server').replace('/', '-')
        package_name = item.get('package_name', server_name)

        return {
            "claude_desktop": {
                "npm": {
                    "mcpServers": {
                        server_name: {
                            "command": "npx",
                            "args": ["-y", package_name]
                        }
                    }
                },
                "docker": {
                    "mcpServers": {
                        server_name: {
                            "command": "docker",
                            "args": ["run", "--rm", "-i", f"{server_name}:latest"]
                        }
                    }
                }
            },
            "cursor": {
                "npm": {
                    "mcpServers": {
                        server_name: {
                            "command": "npx",
                            "args": ["-y", package_name]
                        }
                    }
                }
            },
            "vscode": {
                "servers": {
                    server_name: {
                        "command": "npx",
                        "args": ["-y", package_name]
                    }
                }
            },
            "windsurf": {
                "mcpServers": {
                    server_name: {
                        "command": "npx",
                        "args": ["-y", package_name]
                    }
                }
            }
        }

    def _generate_troubleshooting_guide(self, item: dict) -> dict:
        """Generate troubleshooting guide for the MCP server"""
        return {
            "common_issues": [
                {
                    "issue": "Server not starting",
                    "solution": "Check if all dependencies are installed and environment variables are set correctly"
                },
                {
                    "issue": "Connection timeout",
                    "solution": "Verify network connectivity and server configuration"
                },
                {
                    "issue": "Permission denied",
                    "solution": "Ensure proper file permissions and authentication credentials"
                }
            ],
            "debugging_steps": [
                "Check server logs for error messages",
                "Verify configuration file syntax",
                "Test with MCP inspector",
                "Check environment variables"
            ],
            "log_locations": {
                "claude_desktop": "~/Library/Logs/Claude/mcp*.log",
                "cursor": "Check Cursor output panel",
                "vscode": "Check VS Code output panel"
            }
        }

    def _parse_star_count(self, star_text: str) -> int:
        """Parse star count from various formats like 'Star\n 366', 'Star\n 4.1k', etc."""
        if not star_text:
            return 0

        try:
            # Clean the text - remove 'Star', newlines, and extra whitespace
            cleaned = star_text.replace('Star', '').replace('\n', '').strip()

            if not cleaned:
                return 0

            # Handle 'k' suffix (thousands)
            if cleaned.endswith('k'):
                number_part = cleaned[:-1]
                return int(float(number_part) * 1000)

            # Handle 'M' suffix (millions)
            elif cleaned.endswith('M'):
                number_part = cleaned[:-1]
                return int(float(number_part) * 1000000)

            # Handle regular numbers (remove commas)
            else:
                return int(cleaned.replace(',', ''))

        except (ValueError, TypeError):
            # If parsing fails, return 0
            return 0

    def process_event_data(self, event_data: dict) -> dict:
        """Process a single repository from event data"""
        try:
            # Convert event data to expected format
            item = {
                "title": event_data.get("slug", "").split("/")[-1] if event_data.get("slug") else "",
                "gh_url": event_data.get("repo_url", ""),
                "slug": event_data.get("slug", ""),
                "gh_stars": self._parse_star_count(event_data.get("stars", "")),
                "readme": event_data.get("readme", ""),
                "topics": "",
            }

            # Process single item
            processed_batch = self.process_batch([item])

            if processed_batch:
                result = processed_batch[0]

                # Apply additional SEO enhancement
                if "content" in result:
                    result["content"] = seo_enhancer.enhance_content(result["content"])

                # Publish processed content event
                try:
                    event_bus.publish_event("content.generated", {
                        "repository": event_data,
                        "content": result,
                        "processed_at": datetime.now().isoformat()
                    })
                except Exception as e:
                    self.logger.error(f"Error publishing event: {e}")

                self.logger.info(f"Processed content for {event_data.get('repo_url')}")
                return result

        except Exception as e:
            self.logger.error(f"Error processing event data: {e}")
            self.logger.error(f"Event data that caused error: {event_data}")
            # Log the specific star data that might be causing issues
            if 'stars' in event_data:
                self.logger.error(f"Star data: '{event_data['stars']}'")

        return {}

    def start_event_processing(self):
        """Start processing events from the queue"""
        self.logger.info("Starting event-driven content processing...")

        while True:
            try:
                # Get scraped content events
                event = event_bus.get_events("content.scraped", timeout=30)

                if event:
                    event_data = event["data"]
                    repo_url = event_data.get("repo_url", "")

                    # Skip if already processed
                    if repo_url in self.processed_urls:
                        self.logger.info(f"Skipping already processed: {repo_url}")
                        continue

                    # Process the content
                    result = self.process_event_data(event_data)

                    if result:
                        # Save individual result
                        self._save_data([result])
                        self.processed_urls.add(repo_url)

                        self.logger.info(f"Successfully processed: {repo_url}")

                else:
                    # No events, wait a bit
                    time.sleep(5)

            except KeyboardInterrupt:
                self.logger.info("Event processing stopped by user")
                break
            except Exception as e:
                self.logger.error(f"Error in event processing: {e}")
                time.sleep(10)

    def process_all(self):
        """Process all unprocessed data in batches or start event processing."""
        if self.event_driven:
            self.start_event_processing()
        else:
            data = self._load_data()

            # Filter out already processed items (check multiple URL formats and ID)
            unprocessed_data = []
            for item in data:
                gh_url = item.get('gh_url', '')
                slug = item.get('slug', '')
                title = item.get('title', '')

                # Check if any identifier is already processed
                is_processed = (
                    gh_url in self.processed_urls or
                    slug in self.processed_urls or
                    f"https://github.com/{slug}" in self.processed_urls or
                    self._generate_slug(title) in self.processed_urls
                )

                if not is_processed:
                    unprocessed_data.append(item)

            # Process in batches
            for i in range(0, len(unprocessed_data), self.batch_size):
                batch = unprocessed_data[i:i + self.batch_size]
                print(f"Processing batch {i//self.batch_size + 1} of {len(unprocessed_data)//self.batch_size + 1}")

                processed_batch = self.process_batch(batch)

                # Apply SEO enhancement to each item
                for item in processed_batch:
                    if "content" in item:
                        item["content"] = seo_enhancer.enhance_content(item["content"])

                self._save_data(processed_batch)

                # Update processed URLs
                for item in processed_batch:
                    if 'github' in item and 'url' in item['github'] and item['github']['url']:
                        self.processed_urls.add(item['github']['url'])

                print(f"Completed batch {i//self.batch_size + 1}")

    def process_limited(self, max_repos: int):
        """Process a limited number of repositories."""
        data = self._load_data()

        # Filter out already processed items (check multiple URL formats and ID)
        unprocessed_data = []
        for item in data:
            gh_url = item.get('gh_url', '')
            slug = item.get('slug', '')
            title = item.get('title', '')

            # Check if any identifier is already processed
            is_processed = (
                gh_url in self.processed_urls or
                slug in self.processed_urls or
                f"https://github.com/{slug}" in self.processed_urls or
                self._generate_slug(title) in self.processed_urls
            )

            if not is_processed:
                unprocessed_data.append(item)

        # Limit to max_repos
        limited_data = unprocessed_data[:max_repos]

        print(f"Processing {len(limited_data)} repositories (limited from {len(unprocessed_data)} available)")

        # Process in batches
        for i in range(0, len(limited_data), self.batch_size):
            batch = limited_data[i:i + self.batch_size]
            print(f"Processing batch {i//self.batch_size + 1} of {len(limited_data)//self.batch_size + 1}")

            processed_batch = self.process_batch(batch)

            # Apply SEO enhancement to each item
            for item in processed_batch:
                if "content" in item:
                    item["content"] = seo_enhancer.enhance_content(item["content"])

            self._save_data(processed_batch)

            # Update processed URLs
            for item in processed_batch:
                if 'github' in item and 'url' in item['github'] and item['github']['url']:
                    self.processed_urls.add(item['github']['url'])

            print(f"Completed batch {i//self.batch_size + 1}")

    def process_filtered_urls(self, target_urls: list, max_repos: int = None):
        """Process only repositories matching specific URLs."""
        data = self._load_data()

        # Normalize target URLs for comparison
        normalized_targets = set()
        for url in target_urls:
            # Add various formats of the URL
            normalized_targets.add(url)
            if url.startswith('https://github.com/'):
                slug = url.replace('https://github.com/', '')
                normalized_targets.add(slug)
                normalized_targets.add(f"github.com/{slug}")
            elif '/' in url and not url.startswith('http'):
                normalized_targets.add(f"https://github.com/{url}")
                normalized_targets.add(f"github.com/{url}")

        # Filter data to only include target URLs
        filtered_data = []
        for item in data:
            # Check various URL fields
            repo_url = item.get('repo_url', '')
            gh_url = item.get('gh_url', '')
            slug = item.get('slug', '')

            # Check if any identifier matches our targets
            item_identifiers = {repo_url, gh_url, slug}
            if slug:
                item_identifiers.add(f"https://github.com/{slug}")
                item_identifiers.add(f"github.com/{slug}")

            if item_identifiers & normalized_targets:  # Set intersection
                # Check if already processed
                is_processed = (
                    repo_url in self.processed_urls or
                    gh_url in self.processed_urls or
                    slug in self.processed_urls or
                    f"https://github.com/{slug}" in self.processed_urls
                )

                if not is_processed:
                    filtered_data.append(item)

        if not filtered_data:
            print(f"⚠️  No unprocessed data found for target URLs: {target_urls}")
            print("Available repositories in data:")
            for item in data[:10]:  # Show first 10 for reference
                print(f"  - {item.get('slug', 'N/A')} ({item.get('repo_url', 'N/A')})")
            return

        # Limit if specified
        if max_repos:
            filtered_data = filtered_data[:max_repos]

        print(f"🎯 Processing {len(filtered_data)} repositories matching target URLs")
        for item in filtered_data:
            print(f"  - {item.get('slug', 'N/A')} ({item.get('repo_url', 'N/A')})")

        # Process in batches
        for i in range(0, len(filtered_data), self.batch_size):
            batch = filtered_data[i:i + self.batch_size]
            print(f"Processing batch {i//self.batch_size + 1} of {len(filtered_data)//self.batch_size + 1}")

            processed_batch = self.process_batch(batch)

            # Apply SEO enhancement to each item
            for item in processed_batch:
                if "content" in item:
                    item["content"] = seo_enhancer.enhance_content(item["content"])

            self._save_data(processed_batch)

            # Update processed URLs
            for item in processed_batch:
                if 'github' in item and 'url' in item['github'] and item['github']['url']:
                    self.processed_urls.add(item['github']['url'])

            print(f"Completed batch {i//self.batch_size + 1}")

    def listen_for_events(self):
        """Listen for events continuously."""
        print("🔄 Starting event-driven content generation...")
        print("Listening for 'content.scraped' events...")
        self.start_event_processing()

# Event-driven mode by default, can be changed via command line
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Enhanced Event-Driven Content Processor")
    parser.add_argument("--input-file", default="scrapper/data/05_May_final_repo_data.csv", help="Input file path")
    parser.add_argument("--output-file", default="data/enhanced_output.json", help="Output file path")
    parser.add_argument("--batch-size", type=int, default=2, help="Batch size for processing")
    parser.add_argument("--event-driven", action="store_true", default=True, help="Enable event-driven processing")
    parser.add_argument("--batch-mode", action="store_true", help="Use batch processing instead of event-driven")

    args = parser.parse_args()

    # Create processor
    processor = ContentProcessor(
        input_file=args.input_file,
        output_file=args.output_file,
        batch_size=args.batch_size,
        event_driven=not args.batch_mode
    )

    try:
        processor.process_all()
    except KeyboardInterrupt:
        print("Processing stopped by user")
    except Exception as e:
        print(f"Error in processing: {e}")